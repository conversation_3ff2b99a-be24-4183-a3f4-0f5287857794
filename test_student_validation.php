<?php
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;

echo "=== TEST STUDENT VALIDATION ===\n\n";

// 1. Kiểm tra có sinh viên nào trong database không
echo "1. Checking students in database:\n";
$students = User::where('user_level', 3)->limit(5)->get(['user_code', 'user_login', 'user_level']);
if ($students->count() > 0) {
    foreach ($students as $student) {
        echo "   Student: {$student->user_code} - Login: {$student->user_login} - Level: {$student->user_level}\n";
    }
} else {
    echo "   No students found with user_level = 3\n";
    
    // Kiểm tra có user nào khác không
    echo "\n   Checking other users:\n";
    $users = User::limit(5)->get(['user_code', 'user_login', 'user_level']);
    foreach ($users as $user) {
        echo "   User: {$user->user_code} - Login: {$user->user_login} - Level: {$user->user_level}\n";
    }
}

// 2. Test validation logic
echo "\n2. Testing validation logic:\n";

if ($students->count() > 0) {
    $testStudent = $students->first();
    echo "   Testing with student: {$testStudent->user_code}\n";
    
    // Test valid student
    $validStudent = User::where('user_code', $testStudent->user_code)
        ->where('user_level', 3)
        ->first();
    
    if ($validStudent) {
        echo "   ✅ Valid student found: {$validStudent->user_code} - {$validStudent->user_login}\n";
    } else {
        echo "   ❌ Valid student not found\n";
    }
    
    // Test invalid student (wrong level)
    $invalidStudent = User::where('user_code', $testStudent->user_code)
        ->where('user_level', 1) // Admin level
        ->first();
    
    if ($invalidStudent) {
        echo "   ❌ Should not find student with wrong level\n";
    } else {
        echo "   ✅ Correctly rejected student with wrong level\n";
    }
    
    // Test non-existent student
    $nonExistentStudent = User::where('user_code', 'NONEXISTENT123')
        ->where('user_level', 3)
        ->first();
    
    if ($nonExistentStudent) {
        echo "   ❌ Should not find non-existent student\n";
    } else {
        echo "   ✅ Correctly rejected non-existent student\n";
    }
} else {
    echo "   Cannot test validation - no students in database\n";
    
    // Tạo một sinh viên test
    echo "\n   Creating test student...\n";
    try {
        $testStudent = User::create([
            'user_code' => 'TEST001',
            'user_login' => 'test001',
            'user_level' => 3,
            'password' => bcrypt('password'),
            'email' => '<EMAIL>',
            'name' => 'Test Student'
        ]);
        echo "   ✅ Test student created: {$testStudent->user_code}\n";
    } catch (\Exception $e) {
        echo "   ❌ Failed to create test student: {$e->getMessage()}\n";
    }
}

echo "\n=== END TEST ===\n";
?>
