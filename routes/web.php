<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
use App\Http\Controllers\Admin\TranscriptController;
use App\Http\Controllers\Admin\EducateController;
use Illuminate\Support\Facades\Route;



Route::get('/admin/edu/export/xml', [EducateController::class, 'exportXMLInfoPoint'])->name('export.grade.xml');
Route::get('/', 'HomeController@home')->name('home');
Route::post('login/password', 'Auth\SocialController@loginByPassword')->name('login.password');
Route::get('/dang-xuat', 'Admin\HomeController@logout')->name('logout');
Route::get('login/google', 'Auth\SocialController@redirectToProvider')->name('login.google');
Route::post('2fa/authenticator', 'Admin\HomeController@authenticator')->name('2fa.authenticator');
Route::get('login/google/callback', 'Auth\SocialController@handleProviderCallback');
Route::get('/change/color', 'Admin\HomeController@changeColor')->name('change.color');
Route::get('syncFromCourseResult/{user_login}', [TranscriptController::class, 'syncFromCourseResult'])->name('syncFromCourseResult');
Route::get('exportPdfBangDiemTichLuy/{user_login}', [TranscriptController::class, 'exportPdfBangDiemTichLuy'])->name('exportPdfBangDiemTichLuy');
Route::view('error/no-permission', 'errors.no_permission')->name('no_permission');
Route::get('/login-as-user/{user_id}', 'Auth\SocialController@loginAsUser')->name('login.login_as_user');

// Test routes
Route::get('/test-final', function () {
    return view('test_final_debug');
});

Route::get('/test-simple-table', function () {
    return view('test_debt_simple_table');
});
