<template>
  <div class="kt-container kt-container--fluid kt-grid__item kt-grid__item--fluid">
    <!-- Create, Export, Payment, Cancel Modals -->
    <b-modal v-model="showCreateForm" title="Tạo công nợ mới" size="lg" hide-footer no-close-on-backdrop no-close-on-esc hide-header-close>
      <debt-create-form :terms="terms" @debt-created="onDebtCreated" @cancel="showCreateForm = false" />
    </b-modal>

    <b-modal v-model="showExportForm" title="Xuất dữ liệu công nợ" size="lg" hide-footer no-close-on-backdrop no-close-on-esc hide-header-close>
      <debt-export-form @cancel="showExportForm = false" />
    </b-modal>

    <b-modal v-model="showImportModal" title="Import công nợ từ Excel" size="xl" hide-footer no-close-on-backdrop no-close-on-esc>
      <template #modal-header="{ close }">
        <h4 class="modal-title">
          <i class="la la-file-excel text-success"></i>
          Import công nợ từ Excel
        </h4>
        <button type="button" class="btn-close" @click="close()" aria-label="Close">
          <i class="la la-times"></i>
        </button>
      </template>
      <debt-import @import-completed="onImportCompleted" @cancel="showImportModal = false" />
    </b-modal>

    <b-modal v-model="showPaymentModal" title="Thanh toán công nợ" size="lg" hide-footer>
      <debt-payment-form :debt-id="selectedDebtId" @payment-completed="onPaymentCompleted" @cancel="showPaymentModal = false" />
    </b-modal>

    <b-modal v-model="showCancelModal" title="Hủy công nợ" hide-footer>
      <div class="form-group">
        <label>Bạn có chắc chắn muốn hủy công nợ này?</label>
      </div>
      <div class="form-group">
        <label for="reason">Lý do hủy <span class="text-danger">*</span></label>
        <textarea class="form-control" v-model="cancelReason" rows="3" required></textarea>
      </div>
      <div class="text-right mt-4">
        <button type="button" class="btn btn-secondary mr-2" @click="showCancelModal = false">Hủy bỏ</button>
        <button type="button" class="btn btn-danger" @click="cancelDebt" :disabled="!cancelReason.trim()">Xác nhận hủy</button>
      </div>
    </b-modal>

    <div class="kt-portlet kt-portlet--mobile">
      <div class="kt-portlet__head kt-portlet__head--lg">
        <div class="kt-portlet__head-label">
          <span class="kt-portlet__head-icon"><i class="kt-font-brand flaticon2-line-chart"></i></span>
          <h3 class="kt-portlet__head-title">Danh sách công nợ</h3>
        </div>
        <div class="kt-portlet__head-toolbar">
          <div class="kt-portlet__head-actions">
            <button @click="showExportForm = true" class="btn btn-brand btn-elevate btn-icon-sm mr-2">
              <i class="la la-download"></i> Xuất Excel
            </button>
            <button @click="showImportModal = true" class="btn btn-brand btn-elevate btn-icon-sm ml-2">
              <i class="la la-upload"></i> Import Excel
            </button>
            <button @click="showCreateForm = true" class="btn btn-brand btn-elevate btn-icon-sm">
              <i class="la la-plus"></i> Tạo công nợ
            </button>
          </div>
        </div>
      </div>

      <div class="kt-portlet__body">
        <!-- Search -->
        <div class="kt-form kt-form--label-right kt-margin-t-20 kt-margin-b-10">
          <div class="row align-items-center">
            <div class="col-md-3">
              <input type="text" class="form-control" placeholder="Tìm kiếm..." v-model="filters.keyword" @input="applyFilters">
            </div>
            <div class="col-md-3">
              <select class="form-control" v-model="filters.term_id" @change="applyFilters">
                <option value="">Tất cả kỳ học</option>
                <option v-for="term in terms" :key="term.id" :value="term.id">{{ term.term_name }}</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-control" v-model="filters.status" @change="applyFilters">
                <option value="">Tất cả trạng thái</option>
                <option value="0">Chưa thanh toán</option>
                <option value="1">Đã thanh toán</option>
                <option value="2">Đã hủy</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Loading -->
        <div v-if="loading" class="d-flex justify-content-center my-5">
          <div class="spinner-border text-primary" role="status"><span class="sr-only">Đang tải...</span></div>
        </div>

        <!-- Data Table -->
        <div v-else>
          <div v-if="debts.length === 0" class="alert alert-warning">Không có dữ liệu công nợ nào được tìm thấy.</div>
          <div v-else class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Mã SV</th>
                  <th>Kỳ học</th>
                  <th>Loại phí</th>
                  <th>Số tiền</th>
                  <th>Trạng thái</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="debt in debts" :key="debt.id">
                  <td>{{ debt.id }}</td>
                  <td>{{ debt.user_code || 'N/A' }}</td>
                  <td>{{ debt.term_name || 'N/A' }}</td>
                  <td>{{ debt.fee_type?.name || debt.fee_type_name || 'N/A' }}</td>
                  <td>{{ formatCurrency(debt.amount) }}</td>
                  <td><span :class="getStatusClass(debt.status)">{{ getStatusLabel(debt.status) }}</span></td>
                  <td>
                    <button @click="viewDetail(debt.id)" class="btn btn-sm btn-primary"><i class="la la-eye"></i></button>
                    <button v-if="debt.status === 0" @click="showPaymentForm(debt.id)" class="btn btn-sm btn-success"><i class="la la-money"></i></button>
                    <button v-if="debt.status === 0" @click="showCancelForm(debt.id)" class="btn btn-sm btn-danger"><i class="la la-times-circle"></i></button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination Info -->
          <div class="row mt-3">
            <div class="col-md-6">
              <div class="kt-datatable__pager-info">
                <span class="kt-datatable__pager-detail">
                  Hiển thị {{ ((currentPage - 1) * perPage) + 1 }} đến {{ Math.min(currentPage * perPage, totalRows) }}
                  trong tổng số {{ totalRows }} bản ghi
                </span>
              </div>
            </div>
            <div class="col-md-6">
              <!-- Pagination -->
              <b-pagination
                v-model="currentPage"
                :total-rows="totalRows"
                :per-page="perPage"
                aria-controls="debt-table"
                align="right"
                size="sm"
                @change="changePage"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      debts: [],
      terms: [],
      loading: false,
      filters: {
        keyword: '',
        term_id: '',
        status: ''
      },
      currentPage: 1,
      perPage: 10,
      totalRows: 0,
      showCreateForm: false,
      showExportForm: false,
      showImportModal: false,
      showPaymentModal: false,
      showCancelModal: false,
      selectedDebtId: null,
      cancelReason: ''
    };
  },
  mounted() {
    this.getTerms();
    this.loadDebts();
  },
  methods: {
    getTerms() {
      // Use window.axios to handle interceptor properly
      const axiosInstance = window.axios || axios;

      axiosInstance.get("/api/v1/terms")
        .then(data => {
          // Due to interceptor, we get data directly
          this.terms = Array.isArray(data) ? data : [];
          console.log('Terms loaded:', this.terms.length); // Debug log
        })
        .catch(error => {
          console.error('Error loading terms:', error);
          this.terms = [];
        });
    },
    loadDebts() {
      this.loading = true;
      const params = {
        page: this.currentPage,
        per_page: this.perPage,
        ...this.filters
      };

      // Use window.axios to handle interceptor properly
      const axiosInstance = window.axios || axios;

      axiosInstance.get('/api/v1/debts', { params })
        .then(data => {
          console.log('Debts API response:', data); // Debug log

          // Due to interceptor, we get data directly (not response.data)
          if (Array.isArray(data)) {
            // Simple array response (no pagination)
            this.debts = data.map(d => this.normalizeDebt(d));
            this.totalRows = data.length;
          } else if (data && typeof data === 'object') {
            // Paginated response - data is already the response body due to interceptor
            if (Array.isArray(data.data)) {
              this.debts = data.data.map(d => this.normalizeDebt(d));
              this.totalRows = data.total || 0;
              this.currentPage = data.current_page || 1;
              this.perPage = data.per_page || 10;
            } else if (Array.isArray(data.debts)) {
              // Alternative structure
              this.debts = data.debts.map(d => this.normalizeDebt(d));
              this.totalRows = data.total || data.debts.length;
            } else {
              this.debts = [];
              this.totalRows = 0;
            }
          } else {
            this.debts = [];
            this.totalRows = 0;
          }

          console.log('Processed debts:', this.debts.length, 'Total:', this.totalRows); // Debug log
          console.log('First debt term_name:', this.debts.length > 0 ? this.debts[0].term_name : 'No debts'); // Debug term_name
          console.log('Sample debt object:', this.debts.length > 0 ? this.debts[0] : 'No debts'); // Debug full object
        })
        .catch(() => {
          this.debts = [];
          this.totalRows = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    normalizeDebt(debt) {
      return {
        ...debt,
        user_name: debt.user_name || '',
        fee_type: debt.fee_type || { name: debt.fee_type_name || 'Unknown' }
      };
    },
    applyFilters() {
      this.currentPage = 1;
      this.loadDebts();
    },
    changePage(page) {
      this.currentPage = page;
      this.loadDebts();
    },
    onDebtCreated() {
      this.showCreateForm = false;
      this.loadDebts();
      this.$bvToast.toast('Công nợ đã được tạo thành công!', {
        title: 'Thành công',
        variant: 'success',
        solid: true
      });
    },
    onPaymentCompleted() {
      this.showPaymentModal = false;
      this.loadDebts();
      this.$bvToast.toast('Thanh toán công nợ thành công!', {
        title: 'Thành công',
        variant: 'success',
        solid: true
      });
    },
    onImportCompleted() {
      this.showImportModal = false;
      this.loadDebts();
    },
    cancelDebt() {
      if (!this.cancelReason.trim()) {
        alert('Vui lòng nhập lý do hủy');
        return;
      }

      axios.post(`/api/v1/debts/${this.selectedDebtId}/cancel`, { reason: this.cancelReason })
        .then(() => {
          this.showCancelModal = false;
          this.loadDebts();
          this.$bvToast.toast('Công nợ đã được hủy thành công!', {
            title: 'Thành công',
            variant: 'success',
            solid: true
          });
        })
        .catch(() => {
          alert('Không thể hủy công nợ. Vui lòng thử lại.');
        });
    },
    viewDetail(id) {
      window.location.href = `/admin/debt/${id}/detail`;
    },
    showPaymentForm(id) {
      this.selectedDebtId = id;
      this.showPaymentModal = true;
    },
    showCancelForm(id) {
      this.selectedDebtId = id;
      this.cancelReason = '';
      this.showCancelModal = true;
    },
    formatCurrency(value) {
      return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value || 0);
    },
    getStatusLabel(status) {
      switch (status) {
        case 0: return 'Chưa thanh toán';
        case 1: return 'Đã thanh toán';
        case 2: return 'Đã hủy';
        default: return 'Không xác định';
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 0: return 'kt-badge kt-badge--warning kt-badge--inline';
        case 1: return 'kt-badge kt-badge--success kt-badge--inline';
        case 2: return 'kt-badge kt-badge--danger kt-badge--inline';
        default: return 'kt-badge kt-badge--dark kt-badge--inline';
      }
    }
  },
  components: {
    'debt-create-form': () => import('./DebtCreateForm.vue'),
    'debt-export-form': () => import('./DebtExportForm.vue'),
    'debt-payment-form': () => import('./DebtPaymentForm.vue')
  }
}
</script>
