<template>
  <div>
    <form @submit.prevent="submitForm">
      <div class="form-group row">
        <label class="col-form-label col-lg-3 col-sm-12">Mã sinh viên <span class="text-danger">*</span></label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <input 
            type="text" 
            class="form-control" 
            placeholder="Nhập mã sinh viên" 
            v-model="form.user_code" 
            :class="{ 'is-invalid': errors.user_code }"
            required
          >
          <div v-if="errors.user_code" class="invalid-feedback">{{ errors.user_code[0] }}</div>
          <span class="form-text text-muted">Vui lòng nhập mã sinh viên</span>
        </div>
      </div>
      
      <div class="form-group row">
        <label class="col-form-label col-lg-3 col-sm-12"><PERSON><PERSON> học <span class="text-danger">*</span></label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <select 
            class="form-control" 
            v-model="form.term_id" 
            required
          >
            <option value="">Chọn kỳ học</option>
            <option v-for="term in terms" :key="term.id" :value="term.id">
              {{ term.term_name }}
            </option>
          </select>
          <div v-if="errors.term_id" class="invalid-feedback">{{ errors.term_id[0] }}</div>
        </div>
      </div>
      
      <div class="form-group row">
        <label class="col-form-label col-lg-3 col-sm-12">Loại phí <span class="text-danger">*</span></label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <select 
            class="form-control" 
            v-model="form.fee_type_id" 
            :class="{ 'is-invalid': errors.fee_type_id }"
            required
          >
            <option value="">Chọn loại phí</option>
            <option v-for="feeType in feeTypes" :key="feeType.id" :value="feeType.id">
              {{ feeType.name }}
            </option>
          </select>
          <div v-if="errors.fee_type_id" class="invalid-feedback">{{ errors.fee_type_id[0] }}</div>
        </div>
      </div>
      
      <div class="form-group row">
        <label class="col-form-label col-lg-3 col-sm-12">Mô tả</label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <textarea 
            class="form-control" 
            v-model="form.description" 
            rows="3" 
            placeholder="Nhập mô tả" 
            :class="{ 'is-invalid': errors.description }"
          ></textarea>
          <div v-if="errors.description" class="invalid-feedback">{{ errors.description[0] }}</div>
        </div>
      </div>
      
      <div class="form-group row">
        <label class="col-form-label col-lg-3 col-sm-12">Số tiền gốc <span class="text-danger">*</span></label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <input
            type="number"
            class="form-control"
            v-model="form.original_amount"
            placeholder="Nhập số tiền gốc"
            min="0"
            :class="{ 'is-invalid': errors.original_amount }"
            @input="calculateFinalAmount"
            required
          >
          <div v-if="errors.original_amount" class="invalid-feedback">{{ errors.original_amount[0] }}</div>
          <span class="form-text text-muted">Số tiền trước khi áp dụng giảm giá</span>
        </div>
      </div>

      <!-- Discount Section -->
      <div class="form-group row">
        <label class="col-form-label col-lg-3 col-sm-12">Giảm giá</label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <div class="row">
            <div class="col-md-6">
              <label class="form-label">Phần trăm (%)</label>
              <input
                type="number"
                class="form-control"
                v-model="form.discount_percentage"
                placeholder="0"
                min="0"
                max="100"
                step="0.01"
                :class="{ 'is-invalid': errors.discount_percentage }"
                @input="calculateFinalAmount"
                :disabled="form.discount_amount > 0"
              >
              <div v-if="errors.discount_percentage" class="invalid-feedback">{{ errors.discount_percentage[0] }}</div>
            </div>
            <div class="col-md-6">
              <label class="form-label">Số tiền (VNĐ)</label>
              <input
                type="number"
                class="form-control"
                v-model="form.discount_amount"
                placeholder="0"
                min="0"
                :class="{ 'is-invalid': errors.discount_amount }"
                @input="calculateFinalAmount"
                :disabled="form.discount_percentage > 0"
              >
              <div v-if="errors.discount_amount" class="invalid-feedback">{{ errors.discount_amount[0] }}</div>
            </div>
          </div>
          <span class="form-text text-muted">Chọn một trong hai: phần trăm hoặc số tiền</span>
        </div>
      </div>

      <div class="form-group row" v-if="hasDiscount">
        <label class="col-form-label col-lg-3 col-sm-12">Lý do giảm giá</label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <input
            type="text"
            class="form-control"
            v-model="form.discount_reason"
            placeholder="Nhập lý do giảm giá"
            :class="{ 'is-invalid': errors.discount_reason }"
          >
          <div v-if="errors.discount_reason" class="invalid-feedback">{{ errors.discount_reason[0] }}</div>
        </div>
      </div>

      <!-- Final Amount Display -->
      <div class="form-group row" v-if="form.original_amount > 0">
        <label class="col-form-label col-lg-3 col-sm-12">Số tiền phải trả</label>
        <div class="col-lg-9 col-md-9 col-sm-12">
          <div class="alert alert-info">
            <strong>{{ formatCurrency(finalAmount) }}</strong>
            <span v-if="hasDiscount" class="text-muted">
              (Giảm {{ formatCurrency(discountAmount) }})
            </span>
          </div>
        </div>
      </div>
      
      <div class="text-right mt-4">
        <button type="button" class="btn btn-secondary mr-2" @click="cancel">Hủy</button>
        <button type="submit" class="btn btn-primary" :disabled="submitting">
          <span v-if="submitting" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
          {{ submitting ? 'Đang xử lý...' : 'Tạo công nợ' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  props: {
    terms: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        user_code: '',
        term_id: '',
        fee_type_id: '', // Thay đổi từ fee_type thành fee_type_id
        description: '',
        original_amount: '',
        discount_percentage: 0,
        discount_amount: 0,
        discount_reason: '',
        // Các trường mới từ fee_details
        fee_id: null,
        ki_thu: 1,
        type_fee: null,
        version: 1
      },
      errors: {},
      submitting: false,
      options: {
        terms: []
      },
      feeTypes: [], // Thêm mảng để lưu danh sách loại phí
      finalAmount: 0,
      discountAmount: 0
    }
  },
  computed: {
    hasDiscount() {
      return this.form.discount_percentage > 0 || this.form.discount_amount > 0;
    }
  },
  mounted() {
    // No need to fetch terms separately as they're passed from parent
    // Only use local fetch if no terms were provided via props
    if (!this.terms || this.terms.length === 0) {
      this.getTerms();
    }
    
    // Lấy danh sách loại phí
    this.getFeeTypes();
  },
  methods: {
    getTerms() {
      // Use window.axios to handle interceptor properly
      const axiosInstance = window.axios || axios;

      axiosInstance.get("/api/v1/debts/import/reference-data")
        .then((data) => {
          // Due to interceptor, we get data directly
          console.log('Reference data response:', data); // Debug log
          if (data && data.success && Array.isArray(data.terms)) {
            this.options.terms = data.terms.map((item) => ({
              value: item.id,
              text: item.term_name
            }));
          } else {
            console.error('Invalid terms data format:', data);
            this.options.terms = [];
          }
        })
        .catch((err) => {
          console.error(`Lỗi khi lấy danh sách kỳ học: ${err.message}`);
          this.options.terms = [];
        });
    },
   getFeeTypes() {
        axios
          .get("/api/v1/fee-types")
          .then((res) => {
            if (res.data && Array.isArray(res.data)) {
              this.feeTypes = res.data.filter(item => item.is_active);
            } else {
              console.error('Invalid fee types data format:', res.data);
              this.feeTypes = [];
            }
          })
          .catch((err) => {
            console.error(`Lỗi khi lấy danh sách loại phí: ${err.message}`);
            this.feeTypes = [];
          });
      },
    submitForm() {
      this.submitting = true;
      this.errors = {};
      
      // Update to use the new API endpoint
      axios.post('/api/v1/debts/create', this.form)
        .then(response => {
          this.submitting = false;
          // Kiểm tra các loại response
          if (response.data && (response.data.success || response.data.id)) {
            // Trường hợp API trả về JSON thành công
            this.$emit('debt-created', response.data);
          } else {
            // Nếu server trả về HTML hoặc Redirect, cũng coi là thành công
            this.$emit('debt-created', { success: true });
          }
        })
        .catch(error => {
          this.submitting = false;
          if (error.response && error.response.status === 422) {
            this.errors = error.response.data.errors;
          } else {
            console.error('Error creating debt:', error);
            alert('Có lỗi xảy ra khi tạo công nợ. Vui lòng thử lại.');
          }
        });
    },
    cancel() {
      this.$emit('cancel');
    },
    calculateFinalAmount() {
      const originalAmount = parseFloat(this.form.original_amount) || 0;
      const discountPercentage = parseFloat(this.form.discount_percentage) || 0;
      const discountAmount = parseFloat(this.form.discount_amount) || 0;

      if (discountPercentage > 0) {
        this.discountAmount = originalAmount * (discountPercentage / 100);
        this.finalAmount = originalAmount - this.discountAmount;
        // Clear discount amount when using percentage
        this.form.discount_amount = 0;
      } else if (discountAmount > 0) {
        this.discountAmount = discountAmount;
        this.finalAmount = originalAmount - discountAmount;
        // Clear discount percentage when using amount
        this.form.discount_percentage = 0;
      } else {
        this.discountAmount = 0;
        this.finalAmount = originalAmount;
      }

      // Update the amount field with final amount
      this.form.amount = this.finalAmount;
    },
    formatCurrency(amount) {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(amount || 0);
    }
  }
}
</script>
