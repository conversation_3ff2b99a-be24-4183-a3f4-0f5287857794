<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Debug Test</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <style>
        .debug-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        .debt-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .term-highlight { background: yellow; font-weight: bold; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; }
    </style>
</head>
<body>
    <div id="app">
        <div class="container mt-4">
            <h1>Final Debug Test - Debt & Terms</h1>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="debug-section">
                        <h3>Terms Test</h3>
                        <p>Terms loaded: @{{ terms.length }}</p>
                        <p>Loading terms: @{{ loadingTerms }}</p>
                        <button @click="testTerms" class="btn btn-primary btn-sm">Test Terms API</button>
                        
                        <div v-if="terms.length > 0" class="mt-3">
                            <h5>Terms List:</h5>
                            <ul>
                                <li v-for="term in terms.slice(0, 5)" :key="term.id">
                                    ID: @{{ term.id }} - @{{ term.term_name }}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="debug-section">
                        <h3>Debts Test</h3>
                        <p>Debts loaded: @{{ debts.length }}</p>
                        <p>Loading debts: @{{ loadingDebts }}</p>
                        <button @click="testDebts" class="btn btn-primary btn-sm">Test Debts API</button>
                        
                        <div v-if="debts.length > 0" class="mt-3">
                            <h5>First Debt:</h5>
                            <p>ID: @{{ debts[0].id }}</p>
                            <p>User: @{{ debts[0].user_code }}</p>
                            <p>Term ID: @{{ debts[0].term_id }}</p>
                            <p class="term-highlight">Term Name: @{{ debts[0].term_name || 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="debug-section" v-if="debts.length > 0">
                <h3>Debt Records with Term Names</h3>
                <div v-for="debt in debts.slice(0, 3)" :key="debt.id" class="debt-item">
                    <div class="row">
                        <div class="col-md-8">
                            <strong>Debt ID:</strong> @{{ debt.id }}<br>
                            <strong>User Code:</strong> @{{ debt.user_code }}<br>
                            <strong>Term ID:</strong> @{{ debt.term_id }}<br>
                            <strong class="term-highlight">Term Name:</strong> 
                            <span class="term-highlight">@{{ debt.term_name || 'N/A' }}</span><br>
                            <strong>Fee Type:</strong> @{{ debt.fee_type_name || 'N/A' }}<br>
                            <strong>Amount:</strong> @{{ formatCurrency(debt.amount) }}<br>
                            <strong>Status:</strong> @{{ getStatusLabel(debt.status) }}
                        </div>
                        <div class="col-md-4">
                            <details>
                                <summary>Raw Data</summary>
                                <pre>@{{ JSON.stringify(debt, null, 2) }}</pre>
                            </details>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="debug-section" v-if="apiLogs.length > 0">
                <h3>API Call Logs</h3>
                <div v-for="(log, index) in apiLogs" :key="index" class="mb-2">
                    <small class="text-muted">@{{ log.timestamp }}</small><br>
                    <strong>@{{ log.type }}:</strong> @{{ log.message }}
                    <pre v-if="log.data">@{{ JSON.stringify(log.data, null, 2) }}</pre>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/app.js') }}"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                terms: [],
                debts: [],
                loadingTerms: false,
                loadingDebts: false,
                apiLogs: []
            },
            methods: {
                log(type, message, data = null) {
                    this.apiLogs.unshift({
                        timestamp: new Date().toLocaleTimeString(),
                        type: type,
                        message: message,
                        data: data
                    });
                    console.log(`[${type}] ${message}`, data);
                },
                
                async testTerms() {
                    this.loadingTerms = true;
                    try {
                        this.log('INFO', 'Testing terms API...');
                        
                        const axiosInstance = window.axios || axios;
                        const data = await axiosInstance.get('/api/v1/debts/import/reference-data');
                        
                        this.log('SUCCESS', 'Terms API response received', data);
                        
                        if (data && data.success && Array.isArray(data.terms)) {
                            this.terms = data.terms;
                            this.log('SUCCESS', `Loaded ${this.terms.length} terms`);
                        } else {
                            this.log('ERROR', 'Invalid terms response format', data);
                        }
                        
                    } catch (error) {
                        this.log('ERROR', 'Terms API failed', error);
                    } finally {
                        this.loadingTerms = false;
                    }
                },
                
                async testDebts() {
                    this.loadingDebts = true;
                    try {
                        this.log('INFO', 'Testing debts API...');
                        
                        const axiosInstance = window.axios || axios;
                        const data = await axiosInstance.get('/api/v1/debts', { 
                            params: { limit: 5 } 
                        });
                        
                        this.log('SUCCESS', 'Debts API response received', data);
                        
                        if (data && data.data && Array.isArray(data.data)) {
                            this.debts = data.data;
                            this.log('SUCCESS', `Loaded ${this.debts.length} debts`);
                            this.log('INFO', 'First debt term_name', this.debts[0]?.term_name);
                        } else {
                            this.log('ERROR', 'Invalid debts response format', data);
                        }
                        
                    } catch (error) {
                        this.log('ERROR', 'Debts API failed', error);
                    } finally {
                        this.loadingDebts = false;
                    }
                },
                
                formatCurrency(value) {
                    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value || 0);
                },
                
                getStatusLabel(status) {
                    switch (status) {
                        case 0: return 'Chưa thanh toán';
                        case 1: return 'Đã thanh toán';
                        case 2: return 'Đã hủy';
                        default: return 'Không xác định';
                    }
                }
            },
            mounted() {
                this.log('INFO', 'Vue app mounted');
                this.testTerms();
                this.testDebts();
            }
        });
    </script>
</body>
</html>
