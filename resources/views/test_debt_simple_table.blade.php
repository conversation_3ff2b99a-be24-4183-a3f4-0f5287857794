<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Debt Table Test</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <style>
        .term-highlight { background: yellow; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div id="app">
        <div class="container mt-4">
            <h1>Simple Debt Table Test</h1>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <button @click="loadData" class="btn btn-primary">Load Data</button>
                    <button @click="clearData" class="btn btn-secondary">Clear</button>
                </div>
                <div class="col-md-6">
                    <p>Debts: @{{ debts.length }} | Terms: @{{ terms.length }}</p>
                </div>
            </div>
            
            <!-- Terms Filter -->
            <div class="row mb-3" v-if="terms.length > 0">
                <div class="col-md-4">
                    <label>Filter by Term:</label>
                    <select v-model="selectedTermId" @change="filterDebts" class="form-control">
                        <option value="">All Terms</option>
                        <option v-for="term in terms" :key="term.id" :value="term.id">
                            @{{ term.term_name }}
                        </option>
                    </select>
                </div>
            </div>
            
            <!-- Debts Table -->
            <div v-if="filteredDebts.length > 0">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User Code</th>
                            <th>Term ID</th>
                            <th class="term-highlight">Term Name</th>
                            <th>Fee Type</th>
                            <th>Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="debt in filteredDebts" :key="debt.id">
                            <td>@{{ debt.id }}</td>
                            <td>@{{ debt.user_code }}</td>
                            <td>@{{ debt.term_id }}</td>
                            <td class="term-highlight">@{{ debt.term_name || 'N/A' }}</td>
                            <td>@{{ debt.fee_type_name || 'N/A' }}</td>
                            <td>@{{ formatCurrency(debt.amount) }}</td>
                            <td>@{{ getStatusLabel(debt.status) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div v-else-if="!loading" class="alert alert-warning">
                No debts found
            </div>
            
            <div v-if="loading" class="alert alert-info">
                Loading...
            </div>
        </div>
    </div>

    <script src="{{ asset('js/app.js') }}"></script>
    <script>
        new Vue({
            el: '#app',
            data: {
                debts: [],
                terms: [],
                filteredDebts: [],
                selectedTermId: '',
                loading: false
            },
            methods: {
                async loadData() {
                    this.loading = true;
                    try {
                        const axiosInstance = window.axios || axios;
                        
                        // Load terms
                        const termsData = await axiosInstance.get('/api/v1/debts/import/reference-data');
                        if (termsData && termsData.success && Array.isArray(termsData.terms)) {
                            this.terms = termsData.terms;
                            console.log('Terms loaded:', this.terms.length);
                        }
                        
                        // Load debts
                        const debtsData = await axiosInstance.get('/api/v1/debts', { 
                            params: { limit: 20 } 
                        });
                        if (debtsData && debtsData.data && Array.isArray(debtsData.data)) {
                            this.debts = debtsData.data;
                            this.filteredDebts = [...this.debts];
                            console.log('Debts loaded:', this.debts.length);
                            console.log('Sample debt:', this.debts[0]);
                        }
                        
                    } catch (error) {
                        console.error('Error loading data:', error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                filterDebts() {
                    if (this.selectedTermId === '') {
                        this.filteredDebts = [...this.debts];
                    } else {
                        this.filteredDebts = this.debts.filter(debt => 
                            debt.term_id == this.selectedTermId
                        );
                    }
                    console.log('Filtered debts:', this.filteredDebts.length);
                },
                
                clearData() {
                    this.debts = [];
                    this.terms = [];
                    this.filteredDebts = [];
                    this.selectedTermId = '';
                },
                
                formatCurrency(value) {
                    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value || 0);
                },
                
                getStatusLabel(status) {
                    switch (status) {
                        case 0: return 'Chưa thanh toán';
                        case 1: return 'Đã thanh toán';
                        case 2: return 'Đã hủy';
                        default: return 'Không xác định';
                    }
                }
            },
            mounted() {
                console.log('Vue app mounted');
                this.loadData();
            }
        });
    </script>
</body>
</html>
