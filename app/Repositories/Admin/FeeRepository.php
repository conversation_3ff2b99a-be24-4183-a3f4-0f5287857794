<?php


namespace App\Repositories\Admin;

use App\Imports\VATImport;
use App\Models\Fee\DngRecord;
use App\Exports\Fee\CheckPaymentDngExport;
use Illuminate\Http\Request;
use App\Events\QueueCreate;
use App\Exports\Fee\TransactionExport;
use App\Exports\FeeExport;
use App\Http\Controllers\Admin\SystemController;
use App\Mail\UpdateWallet;
use App\Models\Dra\CurriCulum;
use App\Models\EnglishDetail;
// use App\Models\Fee\FeeDetail; // Đã bỏ bảng fee_details
use App\Models\Fee\FeeLog;
use App\Models\Fee\FeeMail;
use App\Models\Fu\ServiceProcessLog;
use App\Models\Fu\Term;
use App\Http\Lib;
use App\Jobs\UpdateQueueTable;
use App\Models\Fee\Fee;
use App\Models\Fee\Student;
use App\Models\Fee\SubjectAgain;
use App\Models\Fee\Transaction;
use App\Models\Fu\OnlineServices\OnlineService;
use App\Models\Fu\OnlineServices\OnlineServiceLog;
use App\Models\Fu\OnlineServices\ServiceStatus;
use App\Models\Fu\Service;
use App\Models\Fu\ServiceLog;
use App\Models\Fu\ServiceRegister;
use App\Models\Fu\TransferIndustry;
use App\Models\RelearnOnline;
use App\Models\Transcript;
use App\Repositories\BaseRepository;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use App\helper\FeedbackHelper;


class FeeRepository extends BaseRepository
{
    const PHI_KY_TOI = 1;
    const KY_0 = 0;
    const KY_7 = 7;
    const PHI_HOC_KY = 1;
    const PHI_TIENG_ANH = 2;
    const PHI_SACH = 3;
    const PHI_HOC_LAI = 4;
    const CHUA_DAT = -3;
    const TRUOT_DIEM_DANH = -1;
    const id_status_cancel = 5;
    const id_wait_for_payment = 31;
    const PAYMENT_WAITING = 0;
    const PAYMENT_DONE = 1;
    const DA_XEP_LOP = 1;
    const CHUA_XEP_LOP = 0;
    const HUY_XEP_LOP = -1;

    public function getModel()
    {
        return Fee::class;
    }

    public function export($request)
    {
        $status = config('status')->trang_thai_hoc;
        $terms = $this->getTerms();

        return $this->view('fee.export', [
            'status' => $status,
            'terms' => $terms,
        ]);
    }

    public function exportPost($request)
    {
        $type = $request->type;
        switch ($type) {
            case self::PHI_KY_TOI:
                return $this->exportPhiKyToi($request);
                break;
        }
    }

    public function exportPhiKyToi($request)
    {
        $status = $request->status ? implode(',', $request->status) : 'all';
        $term_id = $request->term_id;
        $type = $request->type;
        $user = Auth::user();
        //        $queue = $this->createQueue('fee_statistic', "Thống kê phí kỳ tới status:" . $status . ", term_id: $term_id, type: $type");
        //broadcast(new QueueCreate($user, $queue, 'create'));
        $export = new FeeExport();
        $export->setData($request);
        return Excel::download($export, 'fee.xlsx');

        //        return 'done';
    }

    public function transaction($request)
    {
        $service = $request->service;
        $payment_status = $request->payment_status;
        $keyword = $request->keyword;
        $services = Service::all();
        $type = $request->type;
        if ($type == 'export') {
            return $this->exportConfirm($request);
        }
        $transactions = ServiceLog::with(['service:id,title,name', 'serviceRegister'])
            ->where('type', $service ?? $services->first()->id)
            ->when($payment_status, function ($query, $payment_status) {
                $query->where('payment_status', $payment_status == 1 ? 1 : 0);
            })
            ->when($keyword, function ($query, $keyword) {
                $query->where('user_id', 'like', "%$keyword%");
            })
            ->orderBy('payment_status')
            ->orderBy('created_at', 'desc')
            ->paginate(20);
        foreach ($transactions as $transaction) {
            $subject = '';
            if ($transaction->type == 2) {
                $subject = $transaction->serviceRegister->map(function ($item, $key) {
                    return $item->subject_code;
                });
                $subject = $subject->implode(', ');
            }
            $transaction->temp_created_at = $transaction->created_at->format('d-m-Y');
            if ($service == 23) {
                $subject = $transaction->serviceRegister->map(function ($item, $key) {
                    return $item->subject_name . '-' . $item->relearn_type;
                });
                $subject = $subject->implode('<br>');
            }
            $transaction->subject = $subject;
        }

        return $this->view('fee.transaction', [
            'services' => $services,
            'transactions' => $transactions
        ]);
    }

    public function transactionForm($id, $request)
    {
        $transaction = ServiceLog::with('service:id,title,name')->with('serviceRegister')->where('id', $id)->where('payment_status', 0)->first();
        if (!$transaction) {
            return redirect()->route('admin.fee.transaction');
        }
        if ($transaction->type == 2 and $transaction->payment_status == 0) {
            $service = Service::find(2);
            if (!$service) {
                return $this->redirectWithStatus('danger', 'Dịch vụ không tồn tại');
            }
            if ($service->term_id == null) {
                return $this->redirectWithStatus('danger', 'Chưa chọn học kỳ cho dịch vụ');
            }
            $transaction = $this->checkRealTimePriceSubject($transaction->id, $transaction->user_id, $id, $service);
        }
        if ($transaction->type == 23) {
            foreach ($transaction->serviceRegister as $item) {
                $item->relearn = RelearnOnline::find($item->relearn_id);
            }
        }

        return $this->view('fee.transaction_confirm', [
            'transaction' => $transaction,
        ]);
    }

    public function checkRealTimePriceSubject($service_log_id, $user_login, $id, $service)
    {
        $array = [];
        $tong_tien = 0;
        $service_registers = ServiceRegister::where('service_log_id', $service_log_id)->get();

        foreach ($service_registers as $item) {
            $array[] = $item->subject_code;
        }
        $mon_hoc_lai = Transcript::with(['details' => function ($query) {
            $query->whereIn('status', [self::CHUA_DAT, self::TRUOT_DIEM_DANH]);
        }])->where('user_login', $user_login)
            ->firstOrFail();
        foreach ($mon_hoc_lai->details as $item) {
            $mon_hoc[] = $item->subject_code_pass;
            $discount = false;
            if ($service->term_name == $item->term_name) {
                $discount = true;
            } else {
                $current_term_id = $service->term_id;
                $finish_term_id = Term::where('term_name', $item->term_name)->first()->id;
                if (($finish_term_id + 1) == $current_term_id) {
                    $discount = true;
                }
            }
            $fee_subject = SubjectAgain::where('subject_code', $item->subject_code_pass)->first();

            if ($fee_subject->type == 2) {
                $item->tong_tien = $fee_subject->price;
                $discount = 0;
            } else {
                $item->tong_tien = $discount ? $fee_subject->price / 2 : $fee_subject->price;
                if ($discount) {
                    $discount = $fee_subject->price / 2;
                } else {
                    $discount = 0;
                }
            }
            if (in_array($item->subject_code_pass, $array)) {
                ServiceRegister::where('service_log_id', $service_log_id)
                    ->where('subject_code', $item->subject_code_pass)
                    ->where('skill_code', $item->skill_code_replace ?? $item->skill_code)
                    ->where('user_id', $user_login)
                    ->where('type', 2)
                    ->where('status', 0)
                    ->update([
                        'cost' => $item->tong_tien,
                        'discount' => $discount,
                    ]);
                $tong_tien += $item->tong_tien;
            }
        }

        ServiceLog::where('id', $service_log_id)->where('payment_status', 0)->where('type', 2)->update([
            'cost' => $tong_tien
        ]);

        return ServiceLog::with('service:id,title,name')->with('serviceRegister')->where('id', $id)->where('payment_status', 0)->first();
    }

    public function transactionConfirm($request)
    {
        $term = Lib::getTermDetails();
        $invoice_id = $request->invoice_id;
        $note = $request->note;
        $user_login = null;
        DB::beginTransaction();
        try {
            $this->confirmOrder($request->id, $invoice_id, $term, $note);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e);
            return redirect()->route('admin.fee.transaction');
        }

        return redirect()->route('admin.fee.transaction', ['service' => $request->service_id, 'keyword' => $user_login]);
    }

    public function confirmOrder($id, $invoice_id, $term, $note = null)
    {
        $transaction = ServiceLog::with('service:id,title,name')->with('serviceRegister')->where('id', $id)->where('payment_status', 0)->first();
        if ($transaction) {
            $user_login = $transaction->user_id;
            if ($transaction->type == 2 || $transaction->type == 23) {
                //                $transaction->status = 1;
                ServiceProcessLog::create([
                    'service_log_id' => $id,
                    'user_login' => Auth::user()->user_login,
                    'user_code' => Auth::user()->user_code,
                    'note_report' => 'Đang xử lý',
                    'status' => 1,
                ]);
                foreach ($transaction->serviceRegister as $item) {
                    Transaction::insert([
                        'user_code' => $transaction->user_code,
                        'user_login' => $transaction->user_id,
                        'subject_code' => $item->subject_code,
                        'skill_code' => $item->skill_code,
                        'type' => $transaction->type,
                        'invoice_id' => $invoice_id,
                        'note' => $note,
                        'amount' => $item->cost,
                        'service_log_id' => $transaction->id,
                        'term_name' => $term['term_name'],
                        'created_by' => auth()->user()->user_login,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    if ($item->type == 23) {
                        //                        RelearnOnline::where('id', $item->relearn_id)->update([
                        //                            'payment_status' => 1,
                        //                        ]);
                    }
                }
            } else {
                Transaction::insert([
                    'user_code' => $transaction->user_code,
                    'user_login' => $transaction->user_id,
                    'type' => $transaction->type,
                    'invoice_id' => $invoice_id,
                    'note' => $note,
                    'amount' => $transaction->cost,
                    'service_log_id' => $transaction->id,
                    'term_name' => $term['term_name'],
                    'created_by' => auth()->user()->user_login,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            if ($note) {
                $note = "Chú thích: $note";
            }
            ServiceProcessLog::create([
                'service_log_id' => $transaction->id,
                'user_login' => auth()->user()->user_login,
                'user_code' => auth()->user()->user_login,
                'note_report' => "Cập nhật đơn thành Đã thanh toán<br>$note",
                'status' => 2,
            ]);

            $transaction->payment_status = 1;
            $transaction->save();
        }
    }

    public function importForm()
    {
        return $this->view('fee.import', [
            'terms' => Term::orderBy('id', 'desc')->get()
        ]);
    }

    public function importStore($request)
    {
        $processFeeTerm = $request->get('process_fee', null);
        $date = Carbon::createFromFormat('Y-m-d', '2020-08-08');
        //        dd($date);
        // dd($request->all(), $processFeeTerm == true);
        $reports = [];
        $users = collect([]);
        $fees = collect([]);
        $term = Lib::getTermDetails();
        $whoIs = auth()->user()->user_login;
        $data_insert_success = [];
        if (!$file = $this->importDataFromFile($request->file)) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }

        DB::beginTransaction();
        try {
            foreach ($file as $key => $item) {
                $current_key = "Dòng $key:";
                if (!isset($item[0]) && !isset($item[1]) && !isset($item[2]) && !isset($item[3]) && !isset($item[4])) {
                    $report[] = "$current_key Dữ liệu không đúng";
                    continue;
                }

                $type_extension = null;
                $user_code = strtoupper(trim($item[0]));
                $so_hoa_don = trim($item[1]);
                $so_tien = trim($item[2]);
                $type_fee = trim($item[3]);
                $bill_create = trim($item[4]);
                $note = trim($item[5]);
                $fee_type_accept = ['HP', 'HL', 'KHAC', 'UD'];
                $fee_type_text = [
                    'HP' => 'Học phí',
                    'HL' => 'Phí học lại/Thi lại',
                    'KHAC' => 'Phí khác',
                    'UD' => 'Ưu đãi',
                ];

                $amount = 0;
                if (!in_array($type_fee, $fee_type_accept)) {
                    $report[] = "$current_key Loại phí không đúng";
                    continue;
                }

                if (!$fee = $fees->firstWhere('user_code', $user_code)) {
                    $fee = Fee::where('user_code', $user_code)->get();
                    $fees = $fees->merge($fee);
                    $fee = $fees->firstWhere('user_code', $user_code);
                }

                if (!$fee) {
                    $report[] = "$current_key Chưa có dữ liệu không thể import hoá đơn";
                    continue;
                }

                if ($bill_create) {
                    // $bill_create = Carbon::createFromTimestamp(($bill_create - 25569) * 86400)->format('Y-m-d');
                    if (!is_numeric($bill_create)) {
                        $bill_create = Carbon::createFromFormat('Y-m-d', $bill_create);
                        $bill_create = $bill_create->format('Y-m-d');
                    } else {
                        $bill_create = Date::excelToDateTimeObject($bill_create)->format('Y-m-d');
                    }
                }

                if ($type_fee) {
                    $type_extension = $fee_type_text[$type_fee];
                }

                if (!$user = $users->firstWhere('user_code', $user_code)) {
                    $user = User::where('user_code', $user_code)->get();
                    if ($user->count() == 0) {
                        $report[] = "$current_key $user_code chưa có trong dữ liệu phí";
                        continue;
                    }
                    $users = $users->merge($user);
                    $user = $users->firstWhere('user_code', $user_code);
                }

                Transaction::create([
                    'user_code' => $user->user_code,
                    'user_login' => $user->user_login,
                    'type' => $type_fee,
                    'type_extension' => $type_extension,
                    'invoice_id' => $so_hoa_don,
                    'amount' => $so_tien,
                    'execute' => 1,
                    'in_out' => 1,
                    'note' => $note,
                    'invoice_date_create' => $bill_create,
                    'term_name' => $term['term_name'],
                    'created_by' => $whoIs,
                ]);

                if ($type_fee == 'HP') {
                    $fee->study_wallet = $fee->study_wallet + $so_tien;
                    $amount = $fee->study_wallet;
                }

                if ($type_fee == 'HL') {
                    $fee->relearn_wallet = $fee->relearn_wallet + $so_tien;
                    $amount = $fee->relearn_wallet;
                }

                if ($type_fee == 'KHAC') {
                    $fee->etc_wallet = $fee->etc_wallet + $so_tien;
                    $amount = $fee->etc_wallet;
                }

                if ($type_fee == 'UD') {
                    $fee->promotion_wallet = $fee->promotion_wallet + $so_tien;
                    $amount = $fee->promotion_wallet;
                }

                $fee->cash_in = $fee->cash_in + $so_tien;
                $fee->save();
                $data_insert_success[] = [
                    'user_login' => $user->user_login,
                    'user_code' => $user->user_code,
                    'full_name' => $user->full_name,
                    'wallet_name' => $type_extension,
                    'amount' => $amount,
                    'description' => $note,
                ];

                // DB::rollback();
                // if($processFeeTerm == 'true') {
                //     FeedbackHelper::syncFeeForUser($fee);
                // }
            }

            // DB::rollback();
            // if($processFeeTerm != null && $processFeeTerm != true) {
            //     self::sendEmailWhenUpdateWallet($data_insert_success);
            // }
            DB::commit();
            self::sendEmailWhenUpdateWallet($data_insert_success);

            return $this->redirectWithStatus('success', 'Import thành công', url()->previous());
        } catch (\Exception $e) {
            DB::rollback();
            return $this->redirectWithStatus('danger', 'Import thất bại', url()->previous());
        }
    }

    public function exportConfirm($request)
    {
        return Excel::download(new TransactionExport($request), 'transaction' . now()->format('_d_m_y_h_i_s') . '.xlsx');
    }

    public function importConfirmForm($request)
    {
        return $this->view('fee.transaction_confirm_import');
    }

    public function importConfirmStore($request)
    {
        $reports = [];
        $term = Lib::getTermDetails();
        $whoIs = auth()->user()->user_login;
        if (!$file = $this->importDataFromFile($request->file)) {
            return $this->redirectWithStatus('danger', 'Không có sinh viên nào để import hoặc file tải lên bị lỗi', url()->previous());
        }
        DB::beginTransaction();
        try {
            foreach ($file as $key => $item) {
                $current_key = "Dòng $key:";
                if ($item[12] === null) {
                    $reports[] = "$current_key Dữ liệu không thay đổi";
                    continue;
                }
                if (!isset($item[0]) || !isset($item[12]) || !isset($item[13]) || $item[0] === null || $item[12] === null || $item[13] === null) {
                    $reports[] = "$current_key Dữ liệu không đúng";
                    continue;
                }
                if (!$order = ServiceLog::find($item[0])) {
                    $reports[] = "$current_key Không tìm thấy đơn này";
                    continue;
                }
                if ($order->payment_status == 1) {
                    $reports[] = "$current_key Đơn này đã thanh toán rồi";
                    continue;
                }
                if ($order->type == 2) {
                    $service = Service::find(2);
                    $transaction = $this->checkRealTimePriceSubject($order->id, $order->user_id, $order->id, $service);
                    if ($item[5] != $transaction->cost) {
                        $reports[] = "$current_key Số tiền không đúng: Hoá đơn ($item[5]) - Thực tế ($transaction->cost). Hệ thống đã cập nhật lại số tiền hãy kiểm tra và import lại với hoá đơn này để hoàn tất";
                        continue;
                    }
                }

                $id = $item[0];
                $invoice_id = $item[13];
                $note = $item[14];
                $this->confirmOrder($id, $invoice_id, $term, $note);
                $reports[] = "$current_key Xác nhận thanh toán thành công";
            }
            DB::commit();

            return $this->redirectWithStatus('info', implode('<br>', $reports), url()->previous());
        } catch (\Exception $th) {
            DB::rollback();
            Log::error('Error at ' . $th->getFile() . ' : ' . __METHOD__ . $th->getLine() . ' : ' . $th->getMessage());
            return $this->redirectWithStatus('danger', "Đã xảy ra lỗi", url()->previous());
        }
    }

    public function feeManager($request)
    {
        try {
            $keyword = $request->keyword;
            $action = $request->action;
            $transaction_id = $request->transaction_id;
            $vuot_ky = $request->vuot_ky;
            $pf_transfer = $request->pf_transfer;
            $from_wallet = $request->from_wallet;
            $wallet_type = $request->wallet_type;
            $amount = $request->amount;
            $log_id = $request->log_id;
            $ki_thu = $request->ki_thu;
            $user_code = $request->user_code;
            $term = Lib::getTermDetails();
            $whoIs = auth()->user()->user_login;
            $user = User::where('user_code', $keyword)->where('user_level', 3)->first();
            if ($user) {
                SystemController::createOrUpdateFeeRow($user);
                SystemController::analyzeEnglish($user);
            }

            $fee = Fee::with(['user', 'logs'])->where('user_code', $keyword)->first();
            $english = EnglishDetail::with('level')->where('user_code', $keyword)->first();
            if ($fee) {
                SystemController::syncLogFeeDetailStatus($fee);
                $fee->load('details');
            }

            if ($vuot_ky === 'true') {
                $fee->vuot_ky = 1;
                $fee->save();

                return redirect()->route('admin.fee.manager', [
                    'keyword' => $keyword
                ])->with(['status' => ['type' => 'success', 'messages' => 'Cập nhật vượt kỳ thành công']]);
            }

            if ($vuot_ky === 'false') {
                $fee->vuot_ky = 0;
                $fee->save();

                return redirect()->route('admin.fee.manager', [
                    'keyword' => $keyword
                ])->with(['status' => ['type' => 'warning', 'messages' => 'Huỷ bỏ vượt kỳ thành công']]);
            }

            if ($pf_transfer === 'true') {
                $fee->pf_transfer = 1;
                $fee->version = 0;
                $fee->save();

                return redirect()->route('admin.fee.manager', [
                    'keyword' => $keyword
                ])->with(['status' => ['type' => 'success', 'messages' => 'Cập nhật PF chuyển ngành thành công']]);
            }

            if ($pf_transfer === 'false') {
                $fee->pf_transfer = 0;
                $fee->version = 0;
                $fee->save();

                return redirect()->route('admin.fee.manager', [
                    'keyword' => $keyword
                ])->with(['status' => ['type' => 'warning', 'messages' => 'Huỷ bỏ PF chuyển ngành thành công']]);
            }

            if ($action == 'check_ki_thu') {
                self::syncFeeKiThu($user_code, $ki_thu);

                return redirect()->route('admin.fee.manager', [
                    'keyword' => $user_code
                ])->with(['status' => ['type' => 'success', 'messages' => 'Kiểm tra thành công']]);
            }

            if ($action == 'transaction_cancel') {
                DB::beginTransaction();
                try {
                    $transaction_temp = Transaction::find($transaction_id);
                    if ($transaction_temp) {
                        if ($transaction_temp->type == 'HP') {
                            $fee->study_wallet = $fee->study_wallet - $transaction_temp->amount;
                        } else if ($transaction_temp->type == 'HL') {
                            $fee->relearn_wallet = $fee->relearn_wallet - $transaction_temp->amount;
                        } else if ($transaction_temp->type == 'KHAC') {
                            $fee->etc_wallet = $fee->etc_wallet - $transaction_temp->amount;
                        } else if ($transaction_temp->type == 'UD') {
                            $fee->promotion_wallet = $fee->promotion_wallet - $transaction_temp->amount;
                        }
                        $fee->save();
                        $transaction_temp->execute = 2;
                        $transaction_temp->save();
                    }

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollback();
                }
            } else if ($action == 'transaction_refund') {
                DB::beginTransaction();
                try {
                    $transaction_temp = Transaction::find($transaction_id);
                    if ($transaction_temp) {
                        if ($transaction_temp->type == 'HP') {
                            $fee->study_wallet = $fee->study_wallet + $transaction_temp->amount;
                        } else if ($transaction_temp->type == 'HL') {
                            $fee->relearn_wallet = $fee->relearn_wallet + $transaction_temp->amount;
                        } else if ($transaction_temp->type == 'KHAC') {
                            $fee->etc_wallet = $fee->etc_wallet + $transaction_temp->amount;
                        } else if ($transaction_temp->type == 'UD') {
                            $fee->promotion_wallet = $fee->promotion_wallet + $transaction_temp->amount;
                        }

                        $fee->save();
                        $transaction_temp->execute = 1;
                        $transaction_temp->save();
                    }

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollback();
                }
            } else if ($action == 'transfer_wallet') {
                $wallet_name = [
                    'study_wallet' => 'Ví học phí',
                    'relearn_wallet' => 'Ví học lại/Thi lại',
                    'etc_wallet' => 'Ví khác',
                    'promotion_wallet' => 'Ví ưu đãi',
                ];
                $amount_wallet = $fee->{$from_wallet};
                if ($amount == 0 || ($amount > $amount_wallet)) {
                    return $this->redirectWithStatus('danger', 'Số tiền không đúng, không thể chuyển đổi', route('admin.fee.manager', [
                        'keyword' => $keyword,
                    ]));
                }
                $fee->{$wallet_type} = $fee->{$wallet_type} + $amount;
                $fee->{$from_wallet} = $fee->{$from_wallet} - $amount;
                $fee->save();
                Transaction::create([
                    'user_code' => $fee->user_code,
                    'user_login' => $fee->user_login,
                    'type' => 'CD',
                    'type_extension' => 'Chuyển đổi số dư',
                    'invoice_id' => 0,
                    'amount' => $amount,
                    'execute' => 1,
                    'in_out' => 0,
                    'note' => "Chuyển đổi số dư từ " . $wallet_name[$from_wallet] . " sang " . $wallet_name[$wallet_type],
                    'invoice_date_create' => now(),
                    'term_name' => $term['term_name'],
                    'created_by' => $whoIs,
                ]);
                Transaction::create([
                    'user_code' => $fee->user_code,
                    'user_login' => $fee->user_login,
                    'type' => 'CD',
                    'type_extension' => 'Chuyển đổi số dư',
                    'invoice_id' => 0,
                    'amount' => $amount,
                    'execute' => 1,
                    'in_out' => 1,
                    'note' => "Chuyển đổi số dư từ " . $wallet_name[$from_wallet] . " sang " . $wallet_name[$wallet_type],
                    'invoice_date_create' => now(),
                    'term_name' => $term['term_name'],
                    'created_by' => $whoIs,
                ]);

                return $this->redirectWithStatus('warning', "Chuyển đổi số dư từ " . $wallet_name[$from_wallet] . " sang " . $wallet_name[$wallet_type] . " thành công", route('admin.fee.manager', [
                    'keyword' => $keyword,
                ]));
            } else if ($action == 'log_remove') {
                $fee_log = FeeLog::where('id', $log_id)->first();
                $fee->study_wallet = $fee->study_wallet + $fee_log->amount;
                $fee->version = 0;
                $fee->save();
                $fee_log->delete();

                return $this->redirectWithStatus('warning', "Xoá log thành công", route('admin.fee.manager', [
                    'keyword' => $keyword,
                ]));
            }

            if (!$user) {
                $transactions = Transaction::where('user_code', $keyword)->get();
            } else {
                $transactions = Transaction::where('user_login', $user->user_login)->get();
            }

            if ($fee) {
                $fee->curriculum = CurriCulum::find($fee->curriculum_id);
            }

            $transfer_industry = TransferIndustry::find($fee->ky_chuyen_nganh ?? 0);
            if ($transfer_industry) {
                $transfer_industry->nganh_moi = CurriCulum::find($transfer_industry->id_nganh_moi);
                $transfer_industry->nganh_cu = CurriCulum::find($transfer_industry->id_nganh_cu);
                $transfer_industry->hoc_ky = Term::find($transfer_industry->term_id);
            }

            if ($action == 'transaction_cancel') {
                return redirect(route("admin.fee.manager") . "?keyword=$request->keyword");
            }

            return $this->view('fee.manager', [
                'fee' => $fee,
                'user' => $user,
                'transactions' => $transactions,
                'transfer_industry' => $transfer_industry,
                'english' => $english,
            ]);
        } catch (\Throwable $th) {
            Log::error('--------------fee-manager----------------');
            Log::error('Error at ' . $th->getFile() . ' : ' . __METHOD__ . $th->getLine() . ' : ' . $th->getMessage());
        }
    }

    public static function sendEmailWhenUpdateWallet($data)
    {
        /*
         * Type: array
         * Mẫu: user_login, user_code, họ và tên, tên ví, số dư cuối, chú thích
         */
        if (count($data) > 0) {
            $data = collect($data);
            foreach ($data as $item) {
                $item = (object) $item;
                Mail::to($item->user_login . "")->queue(new UpdateWallet($item));
            }
        }
    }


    public static function processWallet($user, $wallet_name, $amount, $in_out, $type, $term_name, $description)
    {
        $fee_type_text = [
            'study_wallet' => 'Học phí',
            'relearn_wallet' => 'Phí học lại/Thi lại',
            'etc_wallet' => 'Phí khác',
            'promotion_wallet' => 'Ưu đãi',
        ];
        $fee = Fee::where('user_code', $user->user_code)->first();
        if ($fee) {
            $wallet = $fee->{$wallet_name};
            if ($in_out == 1) {
                $wallet = $wallet + $amount;
                $fee->{$wallet_name} = $wallet;
                $fee->save();
            } else {
                if ($wallet >= $amount) {
                    $wallet = $wallet - $amount;
                    $fee->{$wallet_name} = $wallet;
                    $fee->save();
                } else {
                    return 0;
                }
            }
            Transaction::create([
                'user_code' => $fee->user_code,
                'user_login' => $fee->user_login,
                'type' => $type,
                'type_extension' => '',
                'invoice_id' => 0,
                'amount' => $amount,
                'execute' => 1,
                'in_out' => $in_out ? 1 : 0,
                'note' => $description,
                'invoice_date_create' => now(),
                'term_name' => $term_name,
                'created_by' => 'system',
            ]);

            return [
                'user_login' => $user->user_login,
                'user_code' => $user->user_code,
                'full_name' => $user->full_name,
                'wallet_name' => $fee_type_text[$wallet_name],
                'amount' => $wallet,
                'description' => $description,
            ];
        }

        return false;
    }

    public static function syncFeeKiThu($user_code, $hoc_ky_thu_phi)
    {
        $term = Lib::getTermDetails();
        $user = Fee::where('user_code', $user_code)
            ->first();
        $study_wallet = $user->study_wallet;
        // $detail = FeeDetail::where('status', 0)
        //     ->whereIn('type_fee', [1, 2, 3, 4, 5])
        //     ->where('fee_id', $user->id)
        //     ->where('ki_thu', $hoc_ky_thu_phi)
        //     ->get(); // Đã bỏ bảng fee_details
        $detail = []; // Temporary fix
        foreach ($detail as $item) {
            if (($study_wallet - $item->amount) >= 0) {
                $study_wallet = ($study_wallet) - $item->amount;
                $user->study_wallet = $study_wallet;
                $user->save();
                FeeLog::create([
                    'fee_id' => $item->fee_id,
                    'type_fee' => $item->type_fee,
                    'fee_plan_id' => $user->fee_plan_id,
                    'brand_code' => $user->brand_code,
                    'ki_thu' => $item->ki_thu,
                    'amount' => $item->amount,
                    'discount' => $item->discount,
                    'term_id' => $term['term_id'],
                    'term_name' => $term['term_name'],
                    'version' => $item->version,
                ]);
                Transaction::create([
                    'user_code' => $user->user_code,
                    'user_login' => $user->user_login,
                    'type' => 'HP',
                    'type_extension' => 'Học phí',
                    'invoice_id' => 0,
                    'amount' => $item->amount,
                    'execute' => 1,
                    'in_out' => 0,
                    'note' => $user->brand_code . '|' . ($item->ki_thu),
                    'invoice_date_create' => now(),
                    'term_name' => $term['term_name'],
                    'created_by' => 'system',
                ]);
            }
        }
    }
    public function getListCheckPaymentDNG(Request $request)
    {
        $number_record = 8;
        $listDng = "";
        try {
            $listDng_query = $this->filter_list_Dng_query($request);
            $listDng_query
                ->with('dng_invoice', function ($dng_invoice) {
                    $dng_invoice
                        ->select([
                            'ItemId',
                            'InvoiceSerialNumber',
                            'InvoiceDate'
                        ]);
                })
                ->selectRaw("
                    fee_dng_records.item_id as item_id,
                    fee_dng_records.user_login as user_login,
                    fee_dng_records.student_code as user_code,
                    fee_dng_records.dng_type as item_code,
                    fee_dng_records.amount as amount,
                    fee_dng_records.paid_at as paid_date,
                    service_logs.type as service_type_hoc_lai_thi_lai,
                    all_services.name as service_type_khac,
                    service_register.subject_code as subject_code,
                    fee_dng_records.transaction_id as transaction_id,
                    fee_dng_records.dng_id as dng_id,
                    fee_dng_records.is_paid as is_paid,
                    fee_dng_records.paid_chanel as paid_chanel,
                    fee_dng_records.created_at as created_at,
                    fee_dng_records.vat as vat,
                    fee_dng_records.problem as problem,
                    fee_dng_records.note as note,
                    service_register_retest.subject_code as retest_subject_code,
                    fee_dng_records.has_account as has_account
                ")
                ->orderBy('item_id', 'DESC');
            if (isset($request->number_record)) {
                if ($request->number_record != null && $request->number_record != 0) {
                    $number_record = $request->number_record;
                }
            }
            $listDng = $listDng_query->orderBy('dng_id', 'desc')->paginate($number_record);
        } catch (\Throwable $th) {
            Log::error('-----------start err getListCheckPaymentDNG---------');
            Log::error($th);
            Log::error('-----------end err getListCheckPaymentDNG---------');
            return response($th->getLine() . ' - ' . $th->getMessage(), 500);
        }

        return response($listDng, 200);
    }
    public function exportExcelCheckPaymentDng(Request $request)
    {
        try {
            $listDng_query = $this->filter_list_Dng_query($request);
            $listDng_query
                ->with('dng_invoice', function ($dng_invoice) {
                    $dng_invoice
                        ->select([
                            'ItemId',
                            'InvoiceSerialNumber',
                            'InvoiceDate'
                        ]);
                })
                ->select([
                    'fee_dng_records.item_id as item_id',
                    'fee_dng_records.user_login as user_login',
                    'fee_dng_records.student_code as user_code',
                    'fee_dng_records.dng_type as item_code',
                    'fee_dng_records.amount as amount',
                    'fee_dng_records.paid_at as paid_date',
                    'service_logs.type as service_type_hoc_lai_thi_lai',
                    'all_services.name as service_type_khac',
                    'service_register.subject_code as subject_code',
                    'fee_dng_records.transaction_id as transaction_id',
                    'fee_dng_records.dng_id as dng_id',
                    'fee_dng_records.is_paid as is_paid',
                    'fee_dng_records.paid_chanel as paid_chanel',
                    'fee_dng_records.client_transaction_id as client_transaction_id',
                    'fee_dng_records.vat as vat',
                    'service_register_retest.subject_code as retest_subject_code',
                    'fee_dng_records.has_account as has_account'
                ]);
            $listDng = $listDng_query->get();
            $export = new CheckPaymentDngExport($listDng);

            return Excel::download($export, 'doi_soat_DNG.xlsx');
        } catch (\Throwable $th) {
            Log::error('-----------start err exportExcelCheckPaymentDng---------');
            Log::error($th);
            Log::error('-----------end err exportExcelCheckPaymentDng---------');
            return response("", 500);
        }
    }
    public function postUpdateDngVAT(Request $request)
    {
        try {
            $dng_record = DngRecord::where('dng_id', '=', $request->dng_id)->firstOrFail();
            if (isset($request->vat)) {
                if (strlen($request->vat) >= 3 && (!preg_match('/[\'^£$%&*()}{@#~?><>,|=_+¬-]/', $request->vat))) {
                    $dng_record->vat = $request->vat;
                    $dng_record->save();
                } else {
                    return response(['number_updated' => 0], 200);
                }
            } else {
                Log::error("---------start err postUpdateDngVAT------------");
                Log::error("request thiếu VAT");
                Log::error("---------end err postUpdateDngVAT------------");
            }
        } catch (\Throwable $th) {
            Log::error("---------start err postUpdateDngVAT------------");
            Log::error($th);
            Log::error("---------end err postUpdateDngVAT------------");
            return response('error', 500);
        }
        return response(['number_updated' => 1], 200);
    }
    public function postUpdateDngNote(Request $request)
    {
        try {
            $dng_record = DngRecord::where('dng_id', '=', $request->dng_id)->firstOrFail();
            if (isset($request->note)) {
                if (strlen($request->note) > 0) {
                    if ($dng_record->is_paid == 0 && $request->is_paid == 1) {
                        $dng_record->is_paid = 1;
                    }
                    $dng_record->note = $request->note;
                    $dng_record->save();
                } else {
                    return response(['number_updated' => 0], 200);
                }
            } else {
                Log::error("---------start err postUpdateDngNote------------");
                Log::error("request thiếu note");
                Log::error("---------end err postUpdateDngNote------------");
            }
        } catch (\Throwable $th) {
            Log::error("---------start err postUpdateDngNote------------");
            Log::error($th);
            Log::error("---------end err postUpdateDngNote------------");
            return response('error', 500);
        }
        return response(['number_updated' => 1], 200);
    }
    private function filter_list_Dng_query(Request $request)
    {
        try {
            $listDng_query = DngRecord::query();
            // $listDng_query->where('fee_dng_records.is_paid', '=', 1);
            $listDng_query->where(function ($query) {
                $query->where(function ($query_paid) {
                    $query_paid->where('fee_dng_records.is_paid', '=', 1);
                })
                    ->orWhere(function ($query_problem) {
                        $query_problem->where('fee_dng_records.is_paid', '=', 0)
                            ->where('fee_dng_records.problem', '!=', 0);
                    });
            });
            $listDng_query->leftjoin('user', 'user.user_login', '=', 'fee_dng_records.user_login');

            $listDng_query->leftjoin('all_services', function ($join) {
                $join->on('all_services.code', '=', 'fee_dng_records.service_code')
                    ->whereNotNull('fee_dng_records.service_code');
            });
            $listDng_query->leftjoin('service_logs', function ($join) {
                $join->whereNull('fee_dng_records.service_code');
                $join->on('service_logs.id', '=', 'fee_dng_records.item_id');
            });
            $listDng_query->leftjoin('service_register', function ($join) {
                $join->whereNull('fee_dng_records.service_code');
                $join->on('service_register.service_log_id', '=', 'fee_dng_records.item_id');
            });
            $listDng_query->leftjoin('service_register as service_register_retest', function ($join) {
                $join->whereIn('fee_dng_records.service_code', ['dang-ky-thi-lai', 'dang-ky-hoc-lai']);
                $join->on('service_register_retest.online_service_id', '=', 'fee_dng_records.item_id');
            });
            // $listDng_query->leftjoin('service_register', function ($join) {
            //     // $join->whereNull('fee_dng_records.service_code');
            //         $join->where('fee_dng_records.service_code', '=', 'dang-ky-thi-lai');
            //     // $join->on('service_register.service_log_id', '=', 'fee_dng_records.item_id');
            //     $join->on('service_register.online_service_id', '=', 'fee_dng_records.item_id');
            // });
            $listDng_query->with('dng_invoice', function ($dng_invoice) {
                $dng_invoice
                    ->select([
                        'ItemId',
                        'InvoiceSerialNumber',
                        'InvoiceDate'
                    ]);
            });

            if (isset($request->student_code)) {
                if ($request->student_code != "" && $request->student_code != null) {
                    $student_code = $request->student_code;
                    $listDng_query->where(function ($query) use ($student_code) {
                        $query->where('fee_dng_records.user_login', '=', $student_code);
                        $query->orWhere('fee_dng_records.student_code', '=', $student_code);
                    });
                }
            }

            if (isset($request->has_problem)) {
                // if ($request->has_problem > 0) {
                $has_problem = $request->has_problem;
                if ($has_problem == 1) {
                    $listDng_query->where('fee_dng_records.problem', '=', 0);
                }
                if ($has_problem == 2) {
                    $listDng_query->where('fee_dng_records.problem', '=', 1);
                }
                if ($has_problem == 3) {
                    $listDng_query->where('fee_dng_records.problem', '=', 2);
                }
                if ($has_problem == 4) {
                    $listDng_query->where('fee_dng_records.problem', '=', 99);
                }
                if ($has_problem == 5) {
                    $listDng_query->whereNotIn('fee_dng_records.problem', [0, 1, 2, 99]);
                }
                if ($has_problem == 6) {
                    $listDng_query->where('fee_dng_records.problem', '!=', 0);
                }
                // }
            }
            // status_problem
            if (isset($request->status_problem)) {
                $status_problem = $request->status_problem;
                if ($status_problem == 1) {
                    $listDng_query->where('fee_dng_records.is_paid', '=', 0);
                }
                if ($status_problem == 2) {
                    $listDng_query->where('fee_dng_records.is_paid', '=', 1);
                }
            }
            if (isset($request->has_VAT)) {
                if ($request->has_VAT != 2) {
                    $has_VAT = $request->has_VAT;
                    if ($has_VAT == 0) {
                        $listDng_query->whereNull('fee_dng_records.vat');
                    } else if ($has_VAT == 1) {
                        $listDng_query->whereNotNull('fee_dng_records.vat');
                    }
                }
            }

            if (isset($request->is_full_info)) {
                if ($request->is_full_info != 2) {
                    $is_full_info = $request->is_full_info;
                    if ($is_full_info == 0) {
                        $listDng_query->whereNull('fee_dng_records.paid_chanel');
                    } else if ($is_full_info == 1) {
                        $listDng_query->whereNotNull('fee_dng_records.paid_chanel');
                    }
                }
            }


            if (isset($request->datetime_from)) {
                if ($request->datetime_from != 0 && $request->datetime_from != null && $request->datetime_from != "") {
                    $datetime_from = $request->datetime_from;
                    $listDng_query->where(function ($query) use ($datetime_from) {
                        $query->whereDate('fee_dng_records.paid_at', '>=', $datetime_from);
                        $query->orWhereDate('fee_dng_records.created_at', '>=', $datetime_from);
                    });
                }
            }
            if (isset($request->datetime_to)) {
                if ($request->datetime_to != 0 && $request->datetime_to != null && $request->datetime_to != "") {
                    $datetime_to = $request->datetime_to;
                    $listDng_query->where(function ($query) use ($datetime_to) {
                        $query->whereDate('fee_dng_records.paid_at', '<=', $datetime_to);
                        $query->orWhereDate('fee_dng_records.created_at', '<=', $datetime_to);
                    });
                }
            }

            if (isset($request->amount_from)) {
                if ($request->amount_from != 0 && $request->amount_from != null && $request->amount_from != "") {
                    $amount_from = $request->amount_from;
                    $listDng_query->where('fee_dng_records.amount', '>=', $amount_from);
                }
            }

            if (isset($request->amount_to)) {
                if ($request->amount_to != 0 && $request->amount_to != null && $request->amount_to != "") {
                    $amount_to = $request->amount_to;
                    $listDng_query->where('fee_dng_records.amount', '<=', $amount_to);
                }
            }


            if (isset($request->transaction_id)) {
                if ($request->transaction_id != "" && $request->transaction_id != null) {
                    $transaction_id = $request->transaction_id;
                    $listDng_query->where('fee_dng_records.transaction_id', '=', $transaction_id);
                }
            }

            return $listDng_query;
        } catch (\Throwable $th) {
            Log::error("-----------start err filter_list_Dng_query----------------");
            Log::error($th);
            Log::error("----------end err filter_list_Dng_query-----------------");
        }
    }
    private function getVAT($theCollection, $dng_id)
    {
        foreach ($theCollection as $collect) {
            if ($collect['dng_id'] == $dng_id) {
                return [$collect];
            }
        }
        return [];
    }

    public function updateVATByExcel(Request $request)
    {
        try {
            $number_updated = 0;
            $number_error = 0;
            $theCollection = Excel::toArray(new VATImport, $request->file('file'))[0];
            $list_dng_id = array_map(function ($o) {
                return $o['dng_id'];
            }, $theCollection);
            $dng_records = DngRecord::whereIn('dng_id', $list_dng_id);
            $dng_records->each(function ($dng_record) use ($theCollection, &$number_updated, &$number_error) {
                if ($dng_record->vat == null) {
                    $new_VAT = $this->getVAT($theCollection, $dng_record->dng_id);
                    if (count($new_VAT) > 0) {
                        if ($new_VAT[0]['vat'] != null && strlen($new_VAT[0]['vat']) >= 3 && (!preg_match('/[\'^£$%&*()}{@#~?><>,|=_+¬-]/', $new_VAT[0]['vat']))) {
                            $dng_record->vat = $new_VAT[0]['vat'];
                            $dng_record->save();
                            $number_updated++;
                        } else {
                            $number_error++;
                        }
                    } else {
                        $number_error++;
                    }
                } else {
                    $number_error++;
                }
            });
        } catch (\Throwable $th) {
            Log::error('--------start err updateVATByExcel ---------------');
            Log::error($th);
            Log::error('--------end err updateVATByExcel ---------------');
            return response("error", 500);
        }
        return response([
            'number_updated' => $number_updated,
            'number_error' => $number_error
        ], 200);
    }
    public function getFullInfoDng(Request $request)
    {
        try {
            $dng_record = DngRecord::where('dng_id', '=', $request->dng_id)
                ->orderBy('id', 'desc')
                ->firstOrFail();
            $result = $this->getResultDng($dng_record);
            if ($result['code'] == 200 && $result['id'] == $dng_record->dng_id) {
                $dng_record->paid_at = $result['paid_at'];
                $dng_record->paid_chanel = $result['paid_chanel'];
                $dng_record->client_transaction_id = $result['client_transaction_id'];
                $dng_record->save();
            } else {
                return response([
                    'dng_check' => 0,
                    'updated' => 0
                ], 200);
            }
        } catch (\Throwable $th) {
            Log::error('------------ start err getFullInfoDng --------------');
            Log::error($th);
            Log::error('------------ end err getFullInfoDng --------------');
            return response([
                'dng_check' => 0,
                'updated' => 0
            ], 500);
        }
        return response([
            'dng_check' => 1,
            'updated' => 1
        ], 200);
    }
    public function getUpdateAllInfoAllDNG(Request $request)
    {
        $updated = 0;
        $all = 0;
        $error_list = [];
        try {
            $list_paid_dng_record = DngRecord::where('is_paid', '=', 1)
                ->whereNull('paid_at')
                ->whereNull('paid_chanel')
                ->whereNull('client_transaction_id')
                ->get();
            $all = count($list_paid_dng_record);
            foreach ($list_paid_dng_record as $paid_dng_record) {
                $dng_record = DngRecord::where('dng_id', '=', $paid_dng_record->dng_id)
                    ->orderBy('id', 'desc')
                    ->firstOrFail();
                $result = $this->getResultDng($dng_record);
                if ($result['code'] == 200 && $result['id'] == $dng_record->dng_id) {
                    $dng_record->paid_at = $result['paid_at'];
                    $dng_record->paid_chanel = $result['paid_chanel'];
                    $dng_record->client_transaction_id = $result['client_transaction_id'];
                    $dng_record->save();
                    $updated++;
                } else {
                    $error_list[] = [
                        'dng_id' => $dng_record->dng_id,
                        'transaction_id' => $dng_record->transaction_id
                    ];
                }
            }
            return response([
                'all' => $all,
                'updated' => $updated,
                'error_list' => $error_list
            ], 200);
        } catch (\Throwable $th) {
            Log::error('------------ start err getUpdateAllInfoAllDNG --------------');
            Log::error($th);
            Log::error('------------ end err getUpdateAllInfoAllDNG --------------');
            return response([
                'all' => $all,
                'updated' => $updated,
                'error_list' => $error_list
            ], 500);
        }
    }
    public function checkDngByDate(Request $request)
    {

        if (isset($request->check_date_from) && isset($request->check_date_to)) {
            try {
                $date_from = $request->check_date_from;
                $date_to = $request->check_date_to;
                $dng_records_query = DngRecord::where('fee_dng_records.created_at', '>=', $date_from)
                    ->where('fee_dng_records.created_at', '<=', $date_to)
                    ->where('fee_dng_records.is_paid', '=', 0);
                $dng_records_query->join('user', 'user.user_login', '=', 'fee_dng_records.user_login');
                $dng_records_query->leftjoin('all_services', function ($join) {
                    $join->on('all_services.code', '=', 'fee_dng_records.service_code')
                        ->whereNotNull('fee_dng_records.service_code');
                });
                $dng_records_query->leftjoin('online_services', function ($join) {
                    $join->on('online_services.id', '=', 'fee_dng_records.item_id')
                        ->whereNotNull('fee_dng_records.service_code');
                });
                $dng_records_query->leftjoin('service_status', function ($join) {
                    $join->on('online_services.status', '=', 'service_status.id')
                        ->whereNotNull('fee_dng_records.service_code');
                });
                $dng_records_query->leftjoin('service_logs', function ($join) {
                    $join->whereNull('fee_dng_records.service_code');
                    $join->on('service_logs.id', '=', 'fee_dng_records.item_id');
                });
                $dng_records_query->leftjoin('service_register', function ($join) {
                    $join->whereNull('fee_dng_records.service_code');
                    $join->on('service_register.service_log_id', '=', 'fee_dng_records.item_id');
                });
                $dng_records = $dng_records_query->orderBy('fee_dng_records.created_at', 'desc')->get(
                    [
                        'user.user_code as user_code',
                        'fee_dng_records.transaction_id as transaction_id',
                        'fee_dng_records.user_login as user_login',
                        'fee_dng_records.is_paid as is_paid',
                        'fee_dng_records.amount as amount',
                        'fee_dng_records.created_at as created_at',
                        'fee_dng_records.item_id as item_id',
                        'fee_dng_records.dng_id as dng_id',
                        'fee_dng_records.service_code as service_code',
                        'fee_dng_records.dng_type as dng_type',
                        'service_logs.id as hl_service_id',
                        'all_services.name as khac_service_name',
                        'service_logs.type as service_type_hoc_lai_thi_lai',
                        'service_logs.payment_status as hl_service_payment_status',
                        'service_register.status as hl_service_status',
                        'service_status.step as khac_service_status_step',
                        'online_services.status as khac_service_status_id',
                    ]
                );
                $list_resp = [];
                $total_record = count($dng_records);
                $paid_record = [];
                $not_paid_record = [];
                foreach ($dng_records as $record) {
                    $dng_record = DngRecord::where('fee_dng_records.dng_id', '=', $record->dng_id)->firstOrFail();
                    $result = $this->getResultDng($dng_record);
                    $response = $result['response'];
                    $service_name = "";
                    $is_paid = 0;
                    if (in_array($record->dng_type, DngRecord::DNG_TYPE_KHAC_PROCESS) || ($record->dng_type == 'PTL')) {
                        $service_name = $record->khac_service_name;
                        if ($record->khac_service_status_id != self::id_wait_for_payment && $record->is_paid == 1) {
                            $is_paid = 1;
                        }
                    } else if ($record->dng_type == 'HL') {
                        $service_name =  "Học lại";
                        if ($record->hl_service_payment_status == self::PAYMENT_DONE) {
                            $is_paid = 1;
                        }
                    } else {
                        continue;
                    }
                    $resp = [
                        'user_code' => $record->user_code,
                        'item_id' => $dng_record->item_id,
                        'student_account' => $record->user_login,
                        'item_code' => $record->dng_type,
                        'service_name' => $service_name,
                        'amount' => $record->amount,
                        'is_paid_dng' => 0,
                        'is_paid' => $is_paid,
                        'paid_at' => null,
                        'paid_chanel' => null,
                        'created_at' => $record->created_at,
                        'transaction_id' => $record->transaction_id,
                        'problem' => 0
                    ];
                    $time = Carbon::now();
                    $now = $time->toDateTimeString();
                    $term_name = Term::whereDate('startday', '<=', $now)
                        ->whereDate('endday', '>=', $now)
                        ->firstOrFail()->term_name;
                    if ($result['id'] == $dng_record->dng_id) {
                        if ($result['is_paid'] == 1) {

                            $resp['is_paid_dng'] = 1;
                            $resp['paid_at'] = $result['paid_at'];
                            $resp['paid_chanel'] = $result['paid_chanel'];
                            if ($record->dng_type == 'KHAC' || ($record->dng_type == 'HL' && $record->service_code == 'dang-ky-thi-lai')) {
                                if ($record->khac_service_status_id == self::id_wait_for_payment) {
                                    $resp['problem'] = 1;
                                    $dng_record->paid_at = $result['paid_at'];
                                    $dng_record->paid_chanel = $result['paid_chanel'];
                                    $dng_record->save();
                                    $this->synchronizedSuccessPaymentDNG_w_WaitingPaymentService($dng_record, $result, $response, $term_name, $now);
                                } else if (
                                    $record->khac_service_status_id != self::id_wait_for_payment &&
                                    $record->khac_service_status_step == 0
                                ) {
                                    $resp['problem'] = 2;
                                    $this->synchronizedSuccessPaymentDNG_w_CanceledService();
                                } else if (
                                    $record->khac_service_status_id != self::id_wait_for_payment &&
                                    $record->khac_service_status_step > 0
                                ) {
                                    $resp['problem'] = 3;
                                    $this->synchronizedSuccessPaymentDNG_w_PaidService();
                                } else {
                                    $resp['problem'] = 99;
                                    $this->synchronizedUnknownCase();
                                }
                            } else if ($record->dng_type == 'HL' && ($record->service_code == null || $record->service_code == '' || $record->service_code == 0)) {
                                if ($record->hl_service_payment_status == self::PAYMENT_WAITING) {
                                    if ($record->hl_service_status == self::CHUA_XEP_LOP) {
                                        $resp['problem'] = 1;
                                        $this->synchronizedSuccessPaymentDNG_w_WaitingPaymentService($dng_record, $result, $response, $term_name, $now);
                                    } else if ($record->hl_service_status == self::HUY_XEP_LOP) {
                                        $resp['problem'] = 2;
                                        $this->synchronizedSuccessPaymentDNG_w_CanceledService();
                                    } else if ($record->hl_service_status == self::DA_XEP_LOP) {
                                        $resp['problem'] = 3;
                                        $this->synchronizedSuccessPaymentDNG_w_PaidService();
                                    } else {
                                        $resp['problem'] = 99;
                                        $this->synchronizedUnknownCase();
                                    }
                                }
                            } else {
                                $resp['problem'] = 99;
                                $this->synchronizedUnknownCase();
                            }
                        } else {
                            if ($record->dng_type == 'KHAC' || ($record->dng_type == 'HL' && $record->service_code == 'dang-ky-thi-lai')) {
                                if (
                                    $record->khac_service_status_id != self::id_wait_for_payment &&
                                    $record->khac_service_status_step > 0
                                ) {
                                    $resp['problem'] = 4;
                                    $this->synchronizedFailPaymentDNG_w_PaidService();
                                }
                            } else if ($record->dng_type == 'HL' && ($record->service_code == null || $record->service_code == '' || $record->service_code == 0)) {
                                if ($record->hl_service_payment_status == self::PAYMENT_DONE) {
                                    $resp['problem'] = 4;
                                    $this->synchronizedFailPaymentDNG_w_PaidService();
                                }
                            } else {
                                $resp['problem'] = 99;
                                $this->synchronizedUnknownCase();
                            }
                        }
                        $paid_record[] = $resp;
                        $dng_record->problem = $resp['problem'];
                        $dng_record->save();
                    } else {
                        $this->synchronizedUnknownCase();
                        $not_paid_record[] = $resp;
                    }
                    $list_resp[] = $resp;
                }
                return response([
                    'missing' => 0,
                    "check_date_from" => $request->check_date_from,
                    "check_date_to" => $request->check_date_to,
                    'number_paid_record' => $paid_record,
                    'number_not_paid_record' => $not_paid_record,
                    'total' => $total_record,
                    'result' => $list_resp,
                ]);
            } catch (\Throwable $th) {
                Log::error("----------start err checkDngByDate--------------");
                Log::error($th);
                Log::error("----------end err checkDngByDate--------------");
                return response("err", 500);
            }
        } else {
            return response([
                "missing" => 1,
                "check_date_from" => $request->check_date_from,
                "check_date_to" => $request->check_date_to
            ], 200);
        }
    }
    public function checkPaymentDngByItemID(Request $request)
    {
        $item_id = $request->item_id;
        $item_code = $request->item_code;
        try {
            $result = $this->checkPaymentDNG($item_id, 0, null, $item_code);
            return response($result, 200);
        } catch (\Throwable $th) {
            Log::error("----------------start err checkPaymentDngByItemID ----------------");
            Log::error($th);
            Log::error("----------------end err checkPaymentDngByItemID----------------------");
            return response([
                'hasResponse' => false,
                'isPaid' => false
            ], 200);
        }
    }
    /**
     * Case 1;
     * Đơn đang trạng thái đợi thanh toán, chưa hủy.
     * Đơn đã thanh toán trên DNG nhưng DNG chưa trả về trạng thái thanh toán
     */
    public function synchronizedSuccessPaymentDNG_w_WaitingPaymentService($dng_record, $result, $response, $term_name, $now)
    {
        // try {
        //     DB::beginTransaction();
        //     $dng_record = DngRecord::where('id', $dng_record->id)
        //         ->select(['user_login as StudentId', 'transaction_id as TransactionId', 'dng_id as Id', 'dng_type as dng_type'])
        //         ->firstOrFail();
        //     if ($dng_record->dng_type == 'KHAC') {
        //         $result = $this->StorePaidRecord($response, $dng_record, $term_name, $now);
        //     } else {
        //         $result = $this->StorePaidRecord_ThiLai_HocLai($response, $dng_record, $term_name, $now);
        //     }
        //     if ($result) {
        //         DB::commit();
        //     }
        //     DB::rollback();
        // } catch (\Throwable $th) {
        //     DB::rollback();
        // }

    }
    /**
     * Case 2;
     * Đơn đang trạng thái đợi thanh toán, đơn đã hủy.
     * Đơn đã thanh toán trên DNG nhưng DNG chưa trả về trạng thái thanh toán
     */
    public function synchronizedSuccessPaymentDNG_w_CanceledService()
    {
    }
    /**
     * Case 3;
     * Đơn đang trạng thái đã thanh toán, chưa hủy.
     * Đơn đã thanh toán trên DNG nhưng DNG chưa trả về trạng thái thanh toán
     */
    public function synchronizedSuccessPaymentDNG_w_PaidService()
    {
    }

    /**
     * Case 4;
     * Đơn đang trạng thái đã thanh toán, chưa hủy.
     * Đơn chưa thanh toán trên DNG
     */
    public function synchronizedFailPaymentDNG_w_PaidService()
    {
    }
    /**
     * Case 99;
     * Không xác định lỗi
     */
    public function synchronizedUnknownCase()
    {
    }

    private function StorePaidRecord($response, $dng_record, $term_name, $now)
    {
        $result = false;
        try {
            $user = User::find($dng_record->user_login);
            foreach ($response['data'] as $response_record) {
                if ($response_record['StudentId'] == $dng_record->StudentId && $response_record['Id'] == $dng_record->Id && $response_record['IsPaid'] == 1) {
                    $DngRecord = DngRecord::where('dng_id', '=', $dng_record['Id'])
                        ->where('dng_type', '=', $dng_record['dng_type'])
                        ->firstOrFail();
                    $online_service = OnlineService::where('id', '=', $DngRecord->item_id)->firstOrFail();
                    $list_status = ServiceStatus::where('service_code', '=', $DngRecord->service_code)->orderBy('step', 'ASC')->get();
                    if ($online_service->status == $list_status[0]->id) {
                        $DngRecord->problem = 2;
                        $DngRecord->save();
                        return false;
                    }
                    $DngRecord->is_paid = 1;
                    $DngRecord->paid_at = $response_record['PaidAt'];
                    $DngRecord->paid_chanel = $response_record['PaidChanel'];
                    $service = Service::where('code', '=', $DngRecord->service_code)->firstOrFail();
                    $service_name = $service->name;
                    $this->FeeHandle($DngRecord);
                    Transaction::create([
                        'user_code' => $user->user_code,
                        'user_login' => $user->user_login,
                        'type' => $DngRecord->dng_type,
                        'type_extension' => '[Nạp tiền qua Dng' . ' | DNG_ID #' . $DngRecord->dng_id . ' ] ' . $service_name . ' #' . $DngRecord->item_id,
                        'amount' => $response_record['Amount'],
                        'term_name' => $term_name,
                        'created_by' => $user->user_code,
                        'in_out' => 1,
                        'note' => '[Nạp tiền qua Dng' . ' | DNG_ID #' . $DngRecord->dng_id . ' ] ' . $service_name . ' #' . $DngRecord->item_id,
                        'execute' => 1,
                        'dng_transaction_id' => $DngRecord['TransactionId'],
                        'invoice_date_create' => $response_record['PaidAt'],
                        'dng_id' => $response_record['Id']
                    ]);
                    $transaction_id = Transaction::insertGetId([
                        'user_code' => $user->user_code,
                        'user_login' => $user->user_login,
                        'type' => $DngRecord->dng_type,
                        'execute' => 1,
                        'type_extension' => '[Thanh toán dịch vụ] ' . $service_name . ' #' . $DngRecord->item_id,
                        'amount' => $DngRecord->service_amount,
                        'term_name' => $term_name,
                        'created_by' => $user->user_code,
                        'in_out' => 0,
                        'note' => '[Thanh toán dịch vụ] ' . $service_name . ' #' . $DngRecord->item_id,
                        'dng_transaction_id' => null,
                        'invoice_date_create' => $now,
                        'dng_id' => $response_record['Id']
                    ]);

                    $online_service->status = $list_status[1]->id;
                    $user_role_mail = $service->user_role;
                    if ($user_role_mail < 0) {
                        $user_role_mail = $online_service->user_role;
                    }
                    $cost_mail = $service->cost;
                    if ($cost_mail < 0) {
                        $cost_mail = $online_service->cost;
                    }
                    OnlineServiceLog::create([
                        'online_service_id' => $online_service->id,
                        'status' => $list_status[1]->id,
                        'note' => $online_service->note,
                        'user_role' => $online_service->user_role,
                        'student_note' => $online_service->student_note,
                        'staff_user_login' => $online_service->staff_user_login,
                        'transaction_log_id' => $transaction_id,
                        'updated_by' => $user->user_login,
                        'action_note' => '[Thanh toán dịch vụ] ' . $service_name
                    ]);
                    $online_service->save();
                    $DngRecord->save();
                    $result = true;
                    // $this->successRegistedService($online_service->student_user_login, $service_name, $user_role_mail, $cost_mail, $online_service->created_at, $list_status[1]->status_name, "KHAC", "");
                    break;
                }
            }
        } catch (\Throwable $th) {
            Log::error("------StorePaidRecord-------");
            Log::error($th);
        }
        return $result;
    }

    private function StorePaidRecord_ThiLai_HocLai($response, $dng_record, $term_name, $now)
    {
        $result = false;
        try {
            foreach ($response['data'] as $response_record) {
                if ($response_record['StudentId'] == $dng_record->StudentId && $response_record['Id'] == $dng_record->Id && $response_record['IsPaid'] == 1) {
                    $full_dng_record =  DngRecord::where('dng_id', '=', $dng_record['Id'])
                        ->where('dng_type', '=', $dng_record['dng_type'])->firstOrFail();
                    $full_dng_record->is_paid = 1;
                    $full_dng_record->paid_at = $response_record['PaidAt'];
                    $full_dng_record->paid_chanel = $response_record['PaidChanel'];
                    $full_dng_record->save();
                    $student =  User::where('user_login', '=', $dng_record->StudentId)->firstOrFail();
                    $wallet_amount = Fee::where('user_login', '=', $student->user_login)->firstOrFail()->{$full_dng_record->wallet_type};
                    return $this->processHocLaiThiLaiFee($full_dng_record, $student, $term_name, $now, $wallet_amount);
                }
            }
        } catch (\Throwable $th) {
            Log::error("-------Err StorePaidRecord_ThiLai_HocLai -----------------");
            Log::error($th);
            Log::error("-------Err End StorePaidRecord_ThiLai_HocLai -----------------");
        }
        return $result;
    }

    private function processHocLaiThiLaiFee($dng_record, $student, $term_name, $now, $wallet_amount)
    {
        try {
            $order = ServiceLog::where('id', '=', $dng_record->item_id)->firstOrFail();
            if ($order->status == self::HUY_XEP_LOP) {
                $dng_record->problem = 2;
                $dng_record->save();
                Log::error('$order->status == self::HUY_XEP_LOP');
                return false;
            }
            $order->payment_status = self::PAYMENT_DONE;
            $order->save();
            $this->FeeHandle($dng_record);
            $registed_info = ServiceRegister::where('service_log_id', '=', $order->id)->firstOrFail();
            $service_name = 'Đăng ký học lại/thi lại';
            if ($registed_info->type == self::DANG_KY_HOC_LAI) {
                $service_name = 'Đăng ký học lại môn ' . $registed_info->subject_name . ' (' . $registed_info->subject_code . ')';
            } else if ($registed_info->type == self::DANG_KY_THI_LAI) {
                $service_name = 'Đăng ký thi lại môn ' . $registed_info->subject_name . ' (' . $registed_info->subject_code . ')';
            }
            $check_insert_amount = Transaction::create([
                'user_code' => $student->user_code,
                'user_login' => $student->user_login,
                'type' => $dng_record->dng_type,
                'execute' => 1,
                'type_extension' => '[Nạp tiền qua Dng' . ' | DNG_ID #' . $dng_record->dng_id . ' ] ' . $dng_record->wallet_type . ' ' . $service_name . ' #' . $order->id,
                'amount' => $dng_record->amount,
                'term_name' => $term_name,
                'created_by' => $student->user_login,
                'in_out' => 1,
                'note' => '[Nạp tiền qua Dng' . ' | DNG_ID #' . $dng_record->dng_id . '] ' . $dng_record->wallet_type . ' ' . $service_name . ' #' . $order->id,
                'dng_transaction_id' => $dng_record->transaction_id,
                'invoice_date_create' => $now,
                'dng_id' => $dng_record->dng_id
            ]);
            if (!$check_insert_amount) {
                Log::error("cant insert Transaction");
                Log::error('[Nạp tiền qua Dng' . ' | DNG_ID #' . $dng_record->dng_id . ' ] ' . $dng_record->wallet_type . ' ' . $service_name . ' #' . $order->id);
            }
            $check_decrease_amount = Transaction::create([
                'user_code' => $student->user_code,
                'user_login' => $student->user_login,
                'type' => $dng_record->dng_type,
                'execute' => 1,
                'type_extension' => '[Thanh toán dịch vụ] ' . $service_name . ' #' . $order->id,
                'amount' => $dng_record->amount,
                'term_name' => $term_name,
                'created_by' => $student->user_login,
                'in_out' => 0,
                'note' => '[Thanh toán dịch vụ] ' . $service_name . ' #' . $order->id,
                'dng_transaction_id' => $dng_record->transaction_id,
                'invoice_date_create' => $now,
                'dng_id' => $dng_record->dng_id
            ]);
            if (!$check_decrease_amount) {
                Log::error("cant insert Transaction");
                Log::error('[Thanh toán dịch vụ] ' . $service_name . ' #' . $order->id);
            }
            ServiceProcessLog::create([
                'service_log_id' => $order->id,
                'user_login' => $student->user_login,
                'user_code' => $student->user_code,
                'note_report' => "Đã thanh toán",
                'status' => 2,
            ]);
            $user_role = self::id_phong_dao_tao;
            $cost = $dng_record->service_amount;
            // $this->successRegistedService($student->user_login, $service_name, $user_role, $cost, $registed_info->created_at, 'Đã thanh toán', "Học lại/Thi lại","");
        } catch (\Throwable $th) {

            Log::error("------------err processHocLaiThiLai--------");
            Log::error($th);
            Log::error("------------end err processHocLaiThiLai--------");
            return false;
        }
        return true;
    }
}
