<?php

namespace App\Http\Controllers\Admin;

use App\Http\Lib;
use App\Models\EnglishDetail;
use App\Models\EnglishDetailLevel;
use App\Models\Fee\Fee;
// use App\Models\Fee\FeeDetail; // Đã bỏ bảng fee_details
use App\Models\Fee\FeeLog;
use App\Models\Fee\FeeMail;
use App\Models\Fee\Plan;
use App\Models\Fee\PlanDetail;
use App\Models\Fu\Activity;
use App\Models\MienGiamTapTrung;
use App\Models\RelearnOnline;
use App\Models\T7\CourseResult;
use App\Repositories\Admin\FeeRepository;
use App\Repositories\Admin\FeeMailRepository;
use App\Models\Fu\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

use App\Http\DataTables\FeeMaildataTable;


class FeeController extends Controller
{
    protected $FeeMailRepository;
    protected $FeeRepository;

    public function __construct(FeeRepository $feeRepository, FeeMailRepository $feeMailRepository)
    {
        $this->FeeRepository = $feeRepository;
        $this->FeeMailRepository = $feeMailRepository;
    }

    public function export(Request $request)
    {
        return $this->FeeRepository->export($request);
    }

    public function exportPost(Request $request)
    {
        return $this->FeeRepository->exportPost($request);
    }

    public function transaction(Request $request)
    {
        return $this->FeeRepository->transaction($request);
    }

    public function transactionForm($id, Request $request)
    {
        return $this->FeeRepository->transactionForm($id, $request);
    }

    public function transactionConfirm(Request $request)
    {
        return $this->FeeRepository->transactionConfirm($request);
    }

    public function importForm()
    {
        return $this->FeeRepository->importForm();
    }

    public function importStore(Request $request)
    {
        return $this->FeeRepository->importStore($request);
    }

    public static function createOrUpdateFee($user_code)
    {
        $users = User::whereIn('user_code', $user_code)->where('user_level', 3)->get();
        foreach ($users as $user) {
            $fee = Fee::where('user_code', $user->user_code)->first();
            $fee_plan = Plan::with('details')
                ->where('curriculum_id', $user->curriculum_id)
                ->where('brand_code', $user->brand_code)
                ->first();
            if (!$fee_plan) {
                continue;
            }
            $fee_plan->details = PlanDetail::where('fee_plan_id', $fee_plan->id)->get();
            if (!$fee) {
                $fee = Fee::create([
                    'user_code' => $user->user_code,
                    'user_login' => $user->user_login,
                    'study_status' => $user->study_status,
                    'curriculum_id' => $user->curriculum_id,
                    'brand_code' => $user->brand_code,
                    'fee_plan_id' => $fee_plan->id,
                    'ki_thu' => $user->kithu,
                ]);
                foreach ($fee_plan->details as $item) {
                    if (Str::contains($user->user_code, 'PF')) {
                        if ($item->type_fee == 1) {
                            if ($user->brand_code == 'MA') {
                                $cost = 3400000;
                            } else {
                                $cost = 3600000;
                            }
                            // FeeDetail::create([
                            //     'fee_id' => $fee->id,
                            //     'ki_thu' => $item->ki_thu,
                            //     'type_fee' => $item->type_fee,
                            //     'amount' => $cost,
                            //     'discount' => $item->discount,
                            //     'version' => $item->version,
                            // ]); // Đã bỏ bảng fee_details
                        }
                    } else {
                        // FeeDetail::create([
                        //     'fee_id' => $fee->id,
                        //     'ki_thu' => $item->ki_thu,
                        //     'type_fee' => $item->type_fee,
                        //     'amount' => $item->amount,
                        //     'discount' => $item->discount,
                        //     'version' => $item->version,
                        // ]); // Đã bỏ bảng fee_details
                    }
                }
            } else {
                $old_version = $fee->version;
                $new_version = $fee_plan->version;
                $old_brand_code = $fee->brand_code;
                $new_brand_code = $user->brand_code;
                $fee->user_code = $user->user_code;
                $fee->user_login = $user->user_login;
                $fee->study_status = $user->study_status;
                $fee->curriculum_id = $user->curriculum_id;
                $fee->brand_code = $user->brand_code;
                $fee->ki_thu = $user->kithu;
                if ($old_brand_code != $new_brand_code) {
                    $fee->fee_plan_id = $fee_plan->id;
                    $old_version = 0;
                }
                if ($old_version < $new_version) {
                    // FeeDetail::where('fee_id', $fee->id)->whereIn('type_fee', [1,2])->delete();
                    // foreach ($fee_plan->details as $item) {
                    //     FeeDetail::create([
                    //         'fee_id' => $fee->id,
                    //         'ki_thu' => $item->ki_thu,
                    //         'type_fee' => $item->type_fee,
                    //         'amount' => $item->amount,
                    //         'discount' => $item->discount,
                    //         'version' => $item->version,
                    //     ]);
                    // } // Đã bỏ bảng fee_details
                }
                if ($old_brand_code != $new_brand_code) {
                    $last_english = EnglishDetail::where('user_code', $user->user_code)->first();
                    $last_english_details = EnglishDetailLevel::where('english_id', $last_english->id)->orderBy('create_time','desc')->first();
                    if ($last_english_details) {
                        if ($last_english_details->fee_detail_id) {
                            // $fee_f = FeeDetail::where('id', $last_english_details->fee_detail_id)->where('status', 0)->first();
                            // $fee_f->version = $fee_f->version + 1;
                            // $fee_f->ki_thu = $user->kithu + 1;
                            // if ($fee_f->status == 0) {
                            //     $fee_f->save();
                            // } // Đã bỏ bảng fee_details

                        }
                    }
                }
            }
            $fee->save();
//            $fee->details = FeeDetail::where('fee_id', $fee->id)->get();
            $logs = FeeLog::where('fee_id', $fee->id)->get();
            if ($logs->count()) {
                foreach ($logs as $log) {
                    if ($log->brand_code == $fee->brand_code && ($log->type_fee == 1 || $log->type_fee == 2)) {
                        // FeeDetail::where('fee_id', $fee->id)
                        //     ->where('status', '!=', 1)
                        //     ->where('ki_thu', $log->ki_thu)
                        //     ->where('type_fee', $log->type_fee)
                        //     ->update([
                        //         'status' => 1,
                        //     ]); // Đã bỏ bảng fee_details
                    }
                }
            }
        }
    }

    public static function sendFeeMail($term_name, $user_code, $more_fee = 0)
    {
        $fees = Fee::whereIn('user_code', $user_code)->get();
        foreach ($fees as $fee) {
            $ki_thu = $fee->ki_thu + 1;
            if ($ki_thu == 1 || $ki_thu == 8) {
                continue;
            }
            $fee->details = FeeDetail::where('ki_thu', $ki_thu)->where('fee_id', $fee->id)->get();
            if ($fee->details->count()) {
                $user = User::where('user_code', $fee->user_code)->first();
                $hoc_ky = 0;
                $tien_sach = 0;
                $tieng_anh = 0;
                $version = $fee->details->sum('version');
                foreach ($fee->details as $item) {
                    if ($item->type_fee == 1) {
                        $hoc_ky += $item->discount ? (($item->amount * $item->discount) / 100):$item->amount;
                    }
                    if ($item->type_fee == 2) {
                        $tien_sach += $item->discount ? (($item->amount * $item->discount) / 100):$item->amount;
                    }
                    if ($item->type_fee == 3 || $item->type_fee == 4) {
                        $tieng_anh += $item->discount ? (($item->amount * $item->discount) / 100):$item->amount;
                    }
                }
                $so_tien = $hoc_ky + $tien_sach + $tieng_anh + $more_fee;
                $check = FeeMail::where('user_code', $fee->user_code)
                    ->where('term_name', $term_name)
                    ->where('brand_code', $fee->brand_code)
                    ->where('ki_thu', $ki_thu)
                    ->where('version', $version)
                    ->first();
                if (!$check) {
                    $check = FeeMail::create([
                        'user_login' => $fee->user_login,
                        'user_code' => $fee->user_code,
                        'term_name' => $term_name,
                        'brand_code' => $fee->brand_code,
                        'ki_thu' => $ki_thu,
                        'amount' => $so_tien,
                        'hoc_ky' => $hoc_ky,
                        'tien_sach' => $tien_sach,
                        'tieng_anh' => $tieng_anh,
                        'version' => $version,
                    ]);
                }
                if ($check->status == 0) {
                    $so_thu = $check->amount - $fee->study_wallet;
                    if ($so_thu <= 0) {
                        continue;
                    }
                    $fee->full_name = $user->full_name;
                    Mail::to($user->user_login . "")->send(new \App\Mail\FeeMail($check, $fee, 1));
                    $check->status = 1;
                    $check->save();
                    sleep(5);
                }

            }
        }
    }

    public static function analyzeEnglish($user_code)
    {
        $total_relearn = 0;
        $activities = collect([]);
        $users = User::where('user_level', 3)->where('user_code', $user_code)->get();
        foreach ($users as $user) {
            $english = EnglishDetail::where('user_code', $user->user_code)->first();
            $mien_giam = MienGiamTapTrung::where('student_login', $user->user_login)->where('type', 2)->get();
            $relearns = RelearnOnline::where('user_login', $user->user_login)->where('skill_code', 'like','ENT%')->get();
            if (!$english) {
                $english = EnglishDetail::create([
                    'user_code' => $user->user_code,
                    'user_login' => $user->user_login,
                    'study_status' => $user->study_status,
                    'curriculum_id' => $user->curriculum_id,
                    'brand_code' => $user->brand_code,
                ]);
            } else {
                $english->user_code = $user->user_code;
                $english->user_login = $user->user_login;
                $english->study_status = $user->study_status;
                $english->curriculum_id = $user->curriculum_id;
                $english->brand_code = $user->brand_code;
            }
            foreach ($mien_giam as $item) {
                if ($item->skill_code == 'ENT111') {
                    $english->level_1 = 2;
                    $check = EnglishDetailLevel::where('english_id', $english->id)->where('level', 1)->where('status', 2)->first();
                    if (!$check) {
                        EnglishDetailLevel::create([
                            'english_id' => $english->id,
                            'term_id' => 0,
                            'term_name' => 'MG',
                            'subject_code' => $item->subject_code,
                            'skill_code' => $item->skill_code,
                            'subject_name' => 'Tiếng anh 1.1',
                            'status' => 2,
                            'level' => 1,
                            'course_result_id' => 0,
                            'create_time' => '2000-01-01 00:00:00',
                        ]);
                    }
                }
                if ($item->skill_code == 'ENT121') {
                    $english->level_2 = 2;
                    $check = EnglishDetailLevel::where('english_id', $english->id)->where('level', 2)->where('status', 2)->first();
                    if (!$check) {
                        EnglishDetailLevel::create([
                            'english_id' => $english->id,
                            'term_id' => 0,
                            'term_name' => 'MG',
                            'subject_code' => $item->subject_code,
                            'skill_code' => $item->skill_code,
                            'subject_name' => 'Tiếng anh 1.2',
                            'status' => 2,
                            'level' => 2,
                            'course_result_id' => 0,
                            'create_time' => '2000-01-02 00:00:00',
                        ]);
                    }
                }
                if ($item->skill_code == 'ENT211') {
                    $english->level_3 = 2;
                    $check = EnglishDetailLevel::where('english_id', $english->id)->where('level', 3)->where('status', 2)->first();
                    if (!$check) {
                        EnglishDetailLevel::create([
                            'english_id' => $english->id,
                            'term_id' => 0,
                            'term_name' => 'MG',
                            'subject_code' => $item->subject_code,
                            'skill_code' => $item->skill_code,
                            'subject_name' => 'Tiếng anh 2.1',
                            'status' => 2,
                            'level' => 3,
                            'course_result_id' => 0,
                            'create_time' => '2000-01-03 00:00:00',
                        ]);
                    }
                }
                if ($item->skill_code == 'ENT221') {
                    $english->level_4 = 2;
                    $check = EnglishDetailLevel::where('english_id', $english->id)->where('level', 4)->where('status', 2)->first();
                    if (!$check) {
                        EnglishDetailLevel::create([
                            'english_id' => $english->id,
                            'term_id' => 0,
                            'term_name' => 'MG',
                            'subject_code' => $item->subject_code,
                            'skill_code' => $item->skill_code,
                            'subject_name' => 'Tiếng anh 2.2',
                            'status' => 2,
                            'level' => 4,
                            'course_result_id' => 0,
                            'create_time' => '2000-01-04 00:00:00',
                        ]);
                    }
                }
            }
            $english_code = ['ENT111','ENT121','ENT211','ENT221'];
            $course_result = CourseResult::whereIn('skill_code', $english_code)->where('student_login', $user->user_login)->orderBy('create_time')->get();
            foreach ($course_result as $item) {
                $end_date_pre = Carbon::createFromFormat('Y-m-d', $item->end_date)->addDays(35);
                if ($end_date_pre > now()->format('Y-m-d') && !$activity = $activities->firstWhere('groupid', $item->groupid)) {
                    $activity = Activity::select('groupid', DB::raw('min(day) as min'), DB::raw('max(day) as max'))->groupBy('groupid')->get();
                    $activities = $activities->merge($activity);
                    $activity = $activities->firstWhere('groupid', $item->groupid);
                }
                $end_date = Carbon::createFromFormat('Y-m-d', $activity->max ?? $item->end_date)->addDays(5);
                if ($item->val == 0 && ($item->start_date <= now()->format('Y-m-d') && $end_date >= now()->format('Y-m-d'))) {
                    $status = -2;
                } else if ($item->val == 0) {
                    $status = -3;
                } else {
                    $status = $item->val;
                }
                if ($item->skill_code == 'ENT111') {
                    if ($english->level_1 < 1) {
                        if ($item->start_date >= '2019-09-16') {
                            $total_relearn = $relearns->where('skill_code', 'ENT111')->whereNull('term_name_pass')->count();
                            $status = $total_relearn ? -4:$status;
                        }
                        $check = EnglishDetailLevel::where('course_result_id', $item->id)->first();
                        if (!$check) {
                            $check = EnglishDetailLevel::create([
                                'english_id' => $english->id,
                                'term_id' => $item->term_id,
                                'term_name' => $item->pterm_name,
                                'subject_code' => $item->psubject_code,
                                'skill_code' => $item->skill_code,
                                'subject_name' => $item->psubject_name,
                                'status' => $status,
                                'level' => 1,
                                'course_result_id' => $item->id,
                                'create_time' => $item->create_time,
                            ]);
                        } else {
                            $check->status = $status;
                            $check->save();
                        }
                    }
                    $english->level_1 = $status;
                }
                if ($item->skill_code == 'ENT121') {
                    if ($english->level_2 < 1) {
                        if ($item->start_date >= '2019-09-16') {
                            $total_relearn = $relearns->where('skill_code', 'ENT121')->whereNull('term_name_pass')->count();
                            $status = $total_relearn ? -4:$status;
                        }
                        $check = EnglishDetailLevel::where('course_result_id', $item->id)->first();
                        if (!$check) {
                            EnglishDetailLevel::create([
                                'english_id' => $english->id,
                                'term_id' => $item->term_id,
                                'term_name' => $item->pterm_name,
                                'subject_code' => $item->psubject_code,
                                'skill_code' => $item->skill_code,
                                'subject_name' => $item->psubject_name,
                                'status' => $status,
                                'level' => 2,
                                'course_result_id' => $item->id,
                                'create_time' => $item->create_time,
                            ]);
                        } else {
                            $check->status = $status;
                            $check->save();
                        }
                    }
                    $english->level_2 = $status;
                }
                if ($item->skill_code == 'ENT211') {
                    if ($english->level_3 < 1) {
                        if ($item->start_date >= '2019-09-16') {
                            $total_relearn = $relearns->where('skill_code', 'ENT211')->whereNull('term_name_pass')->count();
                            $status = $total_relearn ? -4:$status;
                        }
                        $check = EnglishDetailLevel::where('course_result_id', $item->id)->first();
                        if (!$check) {
                            EnglishDetailLevel::create([
                                'english_id' => $english->id,
                                'term_id' => $item->term_id,
                                'term_name' => $item->pterm_name,
                                'subject_code' => $item->psubject_code,
                                'skill_code' => $item->skill_code,
                                'subject_name' => $item->psubject_name,
                                'status' => $status,
                                'level' => 3,
                                'course_result_id' => $item->id,
                                'create_time' => $item->create_time,
                            ]);
                        } else {
                            $check->status = $status;
                            $check->save();
                        }
                    }
                    $english->level_3 = $status;
                }
                if ($item->skill_code == 'ENT221') {
                    if ($english->level_4 < 1) {
                        if ($item->start_date >= '2019-09-16') {
                            $total_relearn = $relearns->where('skill_code', 'ENT221')->whereNull('term_name_pass')->count();
                            $status = $total_relearn ? -4:$status;
                        }
                        $check = EnglishDetailLevel::where('course_result_id', $item->id)->first();
                        if (!$check) {
                            EnglishDetailLevel::create([
                                'english_id' => $english->id,
                                'term_id' => $item->term_id,
                                'term_name' => $item->pterm_name,
                                'subject_code' => $item->psubject_code,
                                'skill_code' => $item->skill_code,
                                'subject_name' => $item->psubject_name,
                                'status' => $status,
                                'level' => 4,
                                'course_result_id' => $item->id,
                                'create_time' => $item->create_time,
                            ]);
                        } else {
                            $check->status = $status;
                            $check->save();
                        }
                    }
                    $english->level_4 = $status;
                }
            }
            if ($english->level_1 >= 1 && $english->level_2 >= 1 && $english->level_3 >= 1 && $english->level_4 >= 1) {
                $english->finish = 4;
            } else if ($english->level_1 >= 1 && $english->level_2 >= 1 && $english->level_3 >= 1) {
                $english->finish = 3;
            } else if ($english->level_1 >= 1 && $english->level_2 >= 1) {
                $english->finish = 2;
            } else if ($english->level_1 >= 1) {
                $english->finish = 1;
            }
            $english->save();
            $last_english = EnglishDetailLevel::where('english_id', $english->id)->orderBy('create_time','desc')->first();
            $fee = Fee::where('user_login', $user->user_login)->first();
            if ($fee && $last_english) {
                if ($last_english->fee_detail_id == 0) {
                    if ($english->brand_code == 'QTKS' || $english->brand_code == 'QTNH' || $english->brand_code == 'HDDL') {
                        if ($last_english->level == 2 && $last_english->status >= 1) {
                            continue;
                        }
                        if ($last_english->status == 1 || $last_english->status == -2 || $last_english->status == 2 || $english->finish >= 2) {
                            continue;
                        }
                        if ($last_english->level > 2) {
                            continue;
                        }
                        if ($last_english->status == -4) {
                            $amount = 0;
                            if ($total_relearn) {
                                $amount = $total_relearn * 250000;
                            }
                            $result = FeeDetail::create([
                                'fee_id' => $fee->id,
                                'ki_thu' => $fee->ki_thu + 1,
                                'type_fee' => 3,
                                'amount' => $amount,
                                'discount' => 0,
                                'version' => 1,
                                'english_level_id' => $last_english->id,
                                'english_level' => $last_english->level,
                            ]);
                            $last_english->fee_detail_id = $result->id;
                            $last_english->save();
                            continue;
                        }
                        if ($last_english->status == -3 || $last_english->status == -1) {
                            $amount = 2600000;
                            $current_term = 35;
                            $term_failed = $last_english->term_id + 1;
                            if ($term_failed >= $current_term) {
                                $discount = 50;
                            } else {
                                $discount = 0;
                            }
                            $online_subject_code = ['ENT1125','ENT1225','ENT2125','ENT2225'];
                            if (in_array($last_english->subject_code, $online_subject_code)) {
                                $amount = 500000;
                                $discount = 0;
                            }
                            // $result = FeeDetail::create([
                            //     'fee_id' => $fee->id,
                            //     'ki_thu' => $fee->ki_thu + 1,
                            //     'type_fee' => 4,
                            //     'amount' => $amount,
                            //     'discount' => $discount,
                            //     'version' => 1,
                            //     'english_level_id' => $last_english->id,
                            //     'english_level' => $last_english->level,
                            // ]); // Đã bỏ bảng fee_details
                            // $last_english->fee_detail_id = $result->id;
                            // $last_english->save();
                            continue;
                        }
                    }
                    if ($last_english->level == 4 && $english->status >= 1) {
                        continue;
                    }
                    if (!Str::contains($user->user_code, 'PF') && $last_english->level != 4 && ($last_english->status == 1 || $last_english->status == -2 || $last_english->status == 2) && $english->finish < 4) {
                        // $result = FeeDetail::create([
                        //     'fee_id' => $fee->id,
                        //     'ki_thu' => $fee->ki_thu + 1,
                        //     'type_fee' => 3,
                        //     'amount' => 2600000,
                        //     'discount' => 0,
                        //     'version' => 1,
                        //     'english_level_id' => $last_english->id,
                        //     'english_level' => $last_english->level + 1,
                        // ]); // Đã bỏ bảng fee_details
                        // $last_english->fee_detail_id = $result->id;
                        // $last_english->save();
                    }
                    if ($last_english->status == -4) {
                        $amount = 0;
                        if ($total_relearn) {
                            $amount = $total_relearn * 250000;
                        }
                        // $result = FeeDetail::create([
                        //     'fee_id' => $fee->id,
                        //     'ki_thu' => $fee->ki_thu + 1,
                        //     'type_fee' => 3,
                        //     'amount' => $amount,
                        //     'discount' => 0,
                        //     'version' => 1,
                        //     'english_level_id' => $last_english->id,
                        //     'english_level' => $last_english->level,
                        // ]); // Đã bỏ bảng fee_details
                        // $last_english->fee_detail_id = $result->id;
                        // $last_english->save();
                    }
                    if (($last_english->status == -3 || $last_english->status == -1) && $last_english->level < 4) {
                        $amount = 2600000;
                        $current_term = 35;
                        $term_failed = $last_english->term_id + 1;
                        if ($term_failed >= $current_term) {
                            $discount = 50;
                        } else {
                            $discount = 0;
                        }
                        $online_subject_code = ['ENT1125','ENT1225','ENT2125','ENT2225'];
                        if (in_array($last_english->subject_code, $online_subject_code)) {
                            $amount = 500000;
                            $discount = 0;
                        }
                        // $result = FeeDetail::create([
                        //     'fee_id' => $fee->id,
                        //     'ki_thu' => $fee->ki_thu + 1,
                        //     'type_fee' => 4,
                        //     'amount' => $amount,
                        //     'discount' => $discount,
                        //     'version' => 1,
                        //     'english_level_id' => $last_english->id,
                        //     'english_level' => $last_english->level,
                        // ]); // Đã bỏ bảng fee_details
                        // $last_english->fee_detail_id = $result->id;
                        // $last_english->save();
                    }
                } else {
//                    $fee_detail = FeeDetail::
                }
            }
        }
    }

    public function exportConfirm(Request $request)
    {
        return $this->FeeRepository->exportConfirm($request);
    }

    public function importConfirmForm(Request $request)
    {
        return $this->FeeRepository->importConfirmForm($request);
    }

    public function importConfirmStore(Request $request)
    {
        return $this->FeeRepository->importConfirmStore($request);
    }

    public function feeManager(Request $request)
    {
        return $this->FeeRepository->feeManager($request);
    }

    public function getListCheckPaymentDNG(Request $request){
        return $this->FeeRepository->getListCheckPaymentDNG($request);
    }

    public function exportExcelCheckPaymentDng(Request $request){
        return $this->FeeRepository->exportExcelCheckPaymentDng($request);
    }

    public function postUpdateDngVAT(Request $request){
        return $this->FeeRepository->postUpdateDngVAT($request);
    }

    public function postUpdateDngNote(Request $request){
        return $this->FeeRepository->postUpdateDngNote($request);
    }

    public function updateVATByExcel(Request $request){
        return $this->FeeRepository->updateVATByExcel($request);
    }
    
    public function getFullInfoDng(Request $request){
        return $this->FeeRepository->getFullInfoDng($request);
    }

    public function checkDngByDate(Request $request){
        return $this->FeeRepository->checkDngByDate($request);
    }

    public function checkPaymentDngByItemID(Request $request){
        return $this->FeeRepository->checkPaymentDngByItemID($request);
    }

    public function getUpdateAllInfoAllDNG(Request $request){
        return $this->FeeRepository->getUpdateAllInfoAllDNG($request);
    }

    /**
     * 
     * Quét phí chủ động
     * 
     * <AUTHOR>
     * @since 29/08/2022
     * @version 1.0
     */
    public function scanFee(Request $request)
    {
        return $this->FeeMailRepository->scanFee($request);
    }


    /**
     * 
     * Màn hình quản lý mail sinh viên
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function indexFeeMail(Request $request)
    {
        return $this->FeeMailRepository->indexFeeMail($request);
    }


    /**
     * 
     * Màn hình quản lý mail sinh viên
     * 
     * <AUTHOR>
     * @since 15/10/2022
     * @version 1.0
     */
    public function detailFeeMail(Request $request, $id)
    {
        return $this->FeeMailRepository->detailFeeMail($request, $id);
    }


    /**
     * 
     * Cập nhập mail phí sinh viên
     * 
     * <AUTHOR>
     * @since 15/10/2022
     * @version 1.0
     */
    public function updateFeeMail(Request $request, $id)
    {
        return $this->FeeMailRepository->updateFeeMail($id);
    }


    /**
     * 
     * Kiểm tra tiếng anh sau khi hồi phí (sếp lớp sau khi hồi phí)
     * 
     * <AUTHOR>
     * @since 27/10/2022
     * @version 1.0
     */
    public function checkEnglishAgain(Request $request)
    {
        return $this->FeeMailRepository->checkEnglishAgain($request);
    }


    /**
     * 
     * Datatable Fee Mail
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function datatableFeeMail(FeeMaildataTable $datatable)
    {
        return $datatable->build()->toJson();
    }


    /**
     * 
     * Tạo dữ liệu gửi mail theo kỳ
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function createTermFeeMail(Request $request)
    {
        return $this->FeeMailRepository->createTermFeeMail($request);
    }


    /**
     * 
     * Cập nhập thông tin mail phí
     * 
     * <AUTHOR>
     * @since 22/09/2022
     * @version 1.0
     */
    public function updateTermFeeMail(Request $request)
    {
        return $this->FeeMailRepository->updateTermFeeMail($request);
    }


    /**
     * 
     * Tạo dữ liệu gửi mail theo kỳ
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function exportFeeMail(Request $request)
    {
        return $this->FeeMailRepository->exportFeeMail($request);
    }


    /**
     * 
     * Gửi mail cho chưa hoàn thành phí
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function sendMailCurrentTerm(Request $request)
    {
        return $this->FeeMailRepository->sendMailCurrentTerm($request);
    }


    /**
     * 
     * Gửi mail chỉ định
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function sendMailCurrentId(Request $request)
    {
        return $this->FeeMailRepository->sendMailCurrentId($request);
    }


    /**
     * 
     * Gửi mail cho chưa hoàn thành phí
     * 
     * <AUTHOR>
     * @since 06/08/2022
     * @version 1.0
     */
    public function sendMailTest(Request $request)
    {
        return $this->FeeMailRepository->sendMailTest($request);
    }


    /**
     * 
     * Đồng bộ lại mail theo kỳ
     * 
     * <AUTHOR>
     * @since 22/08/2022
     * @version 1.0
     */
    public function syncFeeMail(Request $request)
    {
        return $this->FeeMailRepository->syncFeeMail($request);
    }


    /**
     * 
     * Gửi tạo ví cho sinh viên mới
     * 
     * <AUTHOR>
     * @since 26/09/2022
     * @version 1.0
     */
    public function createFeeForNewUser(Request $request)
    {
        return $this->FeeMailRepository->createFeeForNewUser($request);
    }

    
    public function exportEnglishFee(Request $request)
    {
        return $this->FeeMailRepository->exportEnglishFee($request);
    }


    /**
     * 
     * Cập nhập fee Mail của sinh viên
     * 
     * <AUTHOR>
     * @since 30/09/2022
     * @version 1.0
     */
    public function uploadFeeMailStudent(Request $request)
    {
        return $this->FeeMailRepository->uploadFeeMailStudent($request);
    }

    /**
     * Display fee types management page
     *
     * @return \Illuminate\View\View
     */
    public function feeTypes()
    {
        return view('admin_v1.fee.fee_types.index');
    }
}
